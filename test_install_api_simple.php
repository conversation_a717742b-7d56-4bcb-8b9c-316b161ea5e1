<?php
/**
 * 最简单的install_api.php测试
 */

header("Content-type: text/html; charset=utf-8");

echo "<h1>install_api.php 简单测试</h1>";

if (!file_exists('install_api.php')) {
    echo "<div style='color: red; padding: 20px; border: 1px solid red;'>❌ install_api.php 文件不存在!</div>";
    exit;
}

// 测试GET请求（应该返回只接受POST的消息）
echo "<h2>1. 测试GET请求</h2>";
$url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/install_api.php';

echo "<p><strong>URL:</strong> $url</p>";

$getResponse = @file_get_contents($url);
if ($getResponse === false) {
    echo "<div style='color: red;'>❌ GET请求失败</div>";
} else {
    echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 4px;'>";
    echo "<strong>GET响应内容:</strong><br>";
    echo "<pre>" . htmlspecialchars($getResponse) . "</pre>";
    
    $getData = json_decode($getResponse, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<strong>✅ GET响应JSON格式正确</strong><br>";
        echo "<strong>消息:</strong> " . htmlspecialchars($getData['message'] ?? 'N/A');
    } else {
        echo "<strong>❌ GET响应不是有效JSON</strong>";
    }
    echo "</div>";
}

// 测试POST请求（简单参数）
echo "<hr><h2>2. 测试POST请求（错误参数）</h2>";

$testData = [
    'step' => 'install',
    'hostname' => '',  // 故意留空，应该返回错误
    'database' => '',
    'username' => '',
    'password' => ''
];

$postData = http_build_query($testData);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/x-www-form-urlencoded\r\n" .
                   "Content-Length: " . strlen($postData) . "\r\n",
        'content' => $postData
    ]
]);

echo "<p><strong>POST数据:</strong> " . htmlspecialchars($postData) . "</p>";

$postResponse = @file_get_contents($url, false, $context);
if ($postResponse === false) {
    echo "<div style='color: red;'>❌ POST请求失败</div>";
} else {
    echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 4px;'>";
    echo "<strong>POST响应内容 (" . strlen($postResponse) . " 字符):</strong><br>";
    echo "<pre>" . htmlspecialchars($postResponse) . "</pre>";
    
    $postJsonData = json_decode($postResponse, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<strong>✅ POST响应JSON格式正确</strong><br>";
        echo "<strong>状态:</strong> " . ($postJsonData['status'] ? '成功' : '失败') . "<br>";
        echo "<strong>消息:</strong> " . htmlspecialchars($postJsonData['message'] ?? 'N/A');
    } else {
        echo "<strong>❌ POST响应不是有效JSON:</strong> " . json_last_error_msg() . "<br>";
        
        // 分析问题
        echo "<strong>分析:</strong><br>";
        if (strpos($postResponse, '<html') !== false || strpos($postResponse, '<!DOCTYPE') !== false) {
            echo "- 响应是HTML页面，可能是PHP错误<br>";
        }
        if (strpos($postResponse, 'Fatal error') !== false) {
            echo "- 包含PHP致命错误<br>";
        }
        if (strpos($postResponse, 'Parse error') !== false) {
            echo "- 包含PHP语法错误<br>";
        }
        if (empty(trim($postResponse))) {
            echo "- 响应为空<br>";
        }
        
        $firstChar = substr(trim($postResponse), 0, 1);
        echo "- 首字符: '" . $firstChar . "'<br>";
    }
    echo "</div>";
}

echo "<hr><h2>3. 文件检查</h2>";

// 检查install_api.php文件
$fileSize = filesize('install_api.php');
echo "<p><strong>install_api.php文件大小:</strong> $fileSize bytes</p>";

// 检查文件开头是否有BOM或空白
$fileContent = file_get_contents('install_api.php', false, null, 0, 100);
$hex = bin2hex(substr($fileContent, 0, 20));
echo "<p><strong>文件开头16进制:</strong> $hex</p>";

if (substr($fileContent, 0, 3) === "\xEF\xBB\xBF") {
    echo "<div style='color: red;'>❌ 文件包含BOM，这可能导致输出问题</div>";
} else {
    echo "<div style='color: green;'>✅ 文件开头正常，没有BOM</div>";
}

echo "<hr><h2>4. 服务器环境检查</h2>";
echo "<p><strong>PHP版本:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>错误报告级别:</strong> " . error_reporting() . "</p>";
echo "<p><strong>display_errors:</strong> " . (ini_get('display_errors') ? 'On' : 'Off') . "</p>";
echo "<p><strong>log_errors:</strong> " . (ini_get('log_errors') ? 'On' : 'Off') . "</p>";

echo "<hr><h2>总结</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 4px;'>";
echo "如果上述测试中任何响应不是有效JSON，说明 install_api.php 存在问题。<br>";
echo "请检查PHP错误日志，或者尝试直接在浏览器中访问 install_api.php 查看具体错误。<br><br>";
echo "<strong>下一步:</strong><br>";
echo "1. 如果JSON格式正确，用 <a href='setup_debug.html'>setup_debug.html</a> 测试完整安装流程<br>";
echo "2. 如果JSON格式错误，需要修复 install_api.php 中的问题";
echo "</div>";
?>