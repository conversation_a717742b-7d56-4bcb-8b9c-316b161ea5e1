<?php
/**
 * JSON响应调试工具
 * 专门用于诊断安装API的JSON解析问题
 */

header("Content-type: text/html; charset=utf-8");

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='utf-8'>
    <title>JSON响应调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 4px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .hex-dump { font-family: monospace; font-size: 11px; line-height: 1.2; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔍 JSON响应调试工具</h1>
        <p>专门用于诊断 \"Unexpected token '<'\" 错误</p>";

// 测试配置
$testConfigs = [
    [
        'name' => '当前setup.html使用的API',
        'url' => 'install_api_pdo.php',
        'data' => [
            'step' => 'install',
            'hostname' => 'localhost',
            'database' => 'test_debug_db',
            'username' => 'root',
            'password' => '',
            'hostport' => '3306'
        ]
    ],
    [
        'name' => '原始安装API',
        'url' => 'install_api.php',
        'data' => [
            'step' => 'install',
            'hostname' => 'localhost',
            'database' => 'test_debug_db',
            'username' => 'root',
            'password' => '',
            'hostport' => '3306'
        ]
    ],
    [
        'name' => '简化安装脚本',
        'url' => 'simple_install.php',
        'data' => [
            'step' => 'install',
            'hostname' => 'localhost',
            'database' => 'test_debug_db',
            'username' => 'root',
            'password' => '',
            'hostport' => '3306'
        ]
    ]
];

foreach ($testConfigs as $config) {
    echo "<div class='test-section'>";
    echo "<h2>📡 测试: {$config['name']}</h2>";
    echo "<p><strong>API端点:</strong> <code>{$config['url']}</code></p>";
    
    if (!file_exists($config['url'])) {
        echo "<div class='error'>❌ 文件不存在: {$config['url']}</div>";
        echo "</div>";
        continue;
    }
    
    // 显示测试数据
    echo "<p><strong>测试数据:</strong></p>";
    echo "<pre>" . htmlspecialchars(json_encode($config['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
    
    // 构建POST数据
    $postData = http_build_query($config['data']);
    
    // 创建请求上下文
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                "Content-Type: application/x-www-form-urlencoded",
                "Content-Length: " . strlen($postData),
                "User-Agent: JSON-Debug-Tool/1.0"
            ],
            'content' => $postData,
            'timeout' => 10
        ]
    ]);
    
    try {
        // 发送请求
        $response = file_get_contents($config['url'], false, $context);
        $responseHeaders = $http_response_header ?? [];
        
        echo "<h3>📥 响应分析</h3>";
        
        // 响应头分析
        echo "<p><strong>HTTP响应头:</strong></p>";
        echo "<pre>";
        foreach ($responseHeaders as $header) {
            echo htmlspecialchars($header) . "\n";
        }
        echo "</pre>";
        
        // 响应长度
        echo "<p><strong>响应长度:</strong> " . strlen($response) . " bytes</p>";
        
        // 检查Content-Type
        $contentType = 'unknown';
        foreach ($responseHeaders as $header) {
            if (stripos($header, 'Content-Type') === 0) {
                $contentType = trim(substr($header, strpos($header, ':') + 1));
                break;
            }
        }
        
        if (strpos($contentType, 'application/json') !== false) {
            echo "<div class='success'>✅ Content-Type正确: $contentType</div>";
        } else {
            echo "<div class='error'>❌ Content-Type错误: $contentType</div>";
        }
        
        // 响应内容分析
        echo "<h4>📄 响应内容</h4>";
        
        if (empty($response)) {
            echo "<div class='error'>❌ 响应为空</div>";
        } else {
            // 显示原始响应
            echo "<p><strong>原始响应:</strong></p>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
            
            // 十六进制转储（前200字节）
            echo "<p><strong>十六进制转储（前200字节）:</strong></p>";
            echo "<pre class='hex-dump'>";
            $hex = bin2hex(substr($response, 0, 200));
            for ($i = 0; $i < strlen($hex); $i += 32) {
                $chunk = substr($hex, $i, 32);
                $ascii = '';
                for ($j = 0; $j < strlen($chunk); $j += 2) {
                    $char = chr(hexdec(substr($chunk, $j, 2)));
                    $ascii .= ctype_print($char) ? $char : '.';
                }
                echo sprintf("%08X: %-32s %s\n", $i/2, chunk_split($chunk, 2, ' '), $ascii);
            }
            echo "</pre>";
            
            // JSON解析测试
            echo "<h4>🔍 JSON解析测试</h4>";
            $jsonData = json_decode($response, true);
            $jsonError = json_last_error();
            
            if ($jsonError === JSON_ERROR_NONE) {
                echo "<div class='success'>✅ JSON解析成功!</div>";
                echo "<p><strong>解析结果:</strong></p>";
                echo "<pre>" . htmlspecialchars(json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
            } else {
                echo "<div class='error'>❌ JSON解析失败!</div>";
                echo "<p><strong>错误信息:</strong> " . json_last_error_msg() . "</p>";
                echo "<p><strong>错误代码:</strong> " . $jsonError . "</p>";
                
                // 详细问题分析
                echo "<h5>🔧 问题分析</h5>";
                echo "<ul>";
                
                $firstChar = substr(trim($response), 0, 1);
                if ($firstChar === '<') {
                    echo "<li>❌ 响应以HTML标签开始，可能是PHP错误页面</li>";
                }
                
                if (strpos($response, 'Fatal error') !== false) {
                    echo "<li>❌ 检测到PHP致命错误</li>";
                }
                
                if (strpos($response, 'Warning') !== false) {
                    echo "<li>⚠️ 检测到PHP警告</li>";
                }
                
                if (strpos($response, 'Notice') !== false) {
                    echo "<li>⚠️ 检测到PHP通知</li>";
                }
                
                if (strpos($response, 'Call to undefined function') !== false) {
                    echo "<li>❌ 检测到未定义函数调用</li>";
                }
                
                if (strpos($response, 'mysqli_') !== false) {
                    echo "<li>❌ 检测到mysqli相关错误，可能是扩展未安装</li>";
                }
                
                echo "</ul>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ 请求失败: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo "</div>";
}

// 环境信息
echo "<div class='test-section'>";
echo "<h2>🔧 环境信息</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>项目</th><th>值</th></tr>";
echo "<tr><td>PHP版本</td><td>" . PHP_VERSION . "</td></tr>";
echo "<tr><td>mysqli扩展</td><td>" . (extension_loaded('mysqli') ? '✅ 已加载' : '❌ 未加载') . "</td></tr>";
echo "<tr><td>PDO扩展</td><td>" . (extension_loaded('pdo') ? '✅ 已加载' : '❌ 未加载') . "</td></tr>";
echo "<tr><td>PDO MySQL扩展</td><td>" . (extension_loaded('pdo_mysql') ? '✅ 已加载' : '❌ 未加载') . "</td></tr>";
echo "<tr><td>JSON扩展</td><td>" . (extension_loaded('json') ? '✅ 已加载' : '❌ 未加载') . "</td></tr>";
echo "</table>";
echo "</div>";

echo "<div class='info'>";
echo "<h3>💡 解决建议</h3>";
echo "<p>如果发现JSON解析错误，通常是由以下原因造成的：</p>";
echo "<ol>";
echo "<li><strong>PHP扩展缺失:</strong> 检查mysqli或PDO扩展是否正确安装</li>";
echo "<li><strong>数据库连接失败:</strong> 验证数据库连接参数是否正确</li>";
echo "<li><strong>PHP错误输出:</strong> 检查PHP错误日志，修复代码错误</li>";
echo "<li><strong>输出缓冲问题:</strong> 确保API脚本正确处理输出缓冲</li>";
echo "</ol>";
echo "</div>";

echo "<p><a href='setup.html' class='btn'>返回安装页面</a> <a href='install_status.php' class='btn'>系统状态检查</a></p>";

echo "    </div>
</body>
</html>";
?>
