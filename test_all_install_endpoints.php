<?php
/**
 * 测试所有安装接口的JSON响应
 */

header("Content-type: text/html; charset=utf-8");

echo "<h1>安装接口JSON响应测试</h1>";

// 测试数据
$testData = [
    'step' => 'install',
    'hostname' => 'localhost',
    'database' => 'test_db_' . time(),
    'username' => 'root',
    'password' => '',
    'hostport' => '3306'
];

echo "<h2>测试数据</h2>";
echo "<pre>" . print_r($testData, true) . "</pre>";

// 要测试的接口
$endpoints = [
    'simple_install.php' => '原始安装接口',
    'minimal_install_test.php' => '最小化测试接口'
];

foreach ($endpoints as $endpoint => $description) {
    echo "<hr>";
    echo "<h2>测试: $description ($endpoint)</h2>";
    
    if (!file_exists($endpoint)) {
        echo "<div style='background: #f8d7da; padding: 10px; color: #721c24;'>❌ 文件不存在: $endpoint</div>";
        continue;
    }
    
    try {
        // 构建POST数据
        $postData = http_build_query($testData);
        
        // 创建请求上下文
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    "Content-Type: application/x-www-form-urlencoded",
                    "Content-Length: " . strlen($postData)
                ],
                'content' => $postData,
                'timeout' => 10
            ]
        ]);
        
        $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $endpoint;
        
        // 发送请求
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            echo "<div style='background: #f8d7da; padding: 10px; color: #721c24;'>❌ 请求失败或超时</div>";
            continue;
        }
        
        // 分析响应
        $isJson = false;
        $jsonData = null;
        $jsonError = null;
        
        $jsonData = json_decode($response, true);
        $jsonError = json_last_error();
        $isJson = ($jsonError === JSON_ERROR_NONE);
        
        echo "<div style='margin: 10px 0;'>";
        echo "<strong>响应长度:</strong> " . strlen($response) . " bytes<br>";
        echo "<strong>响应预览:</strong> " . htmlspecialchars(substr($response, 0, 200)) . (strlen($response) > 200 ? '...' : '') . "<br>";
        echo "</div>";
        
        if ($isJson) {
            echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 4px; color: #155724;'>";
            echo "✅ JSON解析成功<br>";
            echo "<strong>状态:</strong> " . ($jsonData['status'] ? '成功' : '失败') . "<br>";
            echo "<strong>消息:</strong> " . htmlspecialchars($jsonData['message']) . "<br>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 4px; color: #721c24;'>";
            echo "❌ JSON解析失败<br>";
            echo "<strong>错误:</strong> " . json_last_error_msg() . "<br>";
            
            // 分析问题
            if (strpos($response, '<') !== false) {
                echo "<strong>问题:</strong> 响应包含HTML标签<br>";
            }
            if (strpos($response, 'Fatal error') !== false) {
                echo "<strong>问题:</strong> PHP致命错误<br>";
            }
            if (strpos($response, 'Warning') !== false) {
                echo "<strong>问题:</strong> PHP警告<br>";
            }
            
            echo "<strong>原始响应:</strong><br>";
            echo "<textarea style='width: 100%; height: 100px; font-family: monospace;'>";
            echo htmlspecialchars($response);
            echo "</textarea>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 10px; color: #721c24;'>";
        echo "❌ 测试异常: " . $e->getMessage();
        echo "</div>";
    }
}

echo "<hr>";
echo "<h2>使用建议</h2>";
echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 4px;'>";
echo "1. 如果 <strong>minimal_install_test.php</strong> 工作正常，说明基础功能没问题<br>";
echo "2. 如果 <strong>simple_install.php</strong> 有问题，可能是复杂的SQL处理导致的<br>";
echo "3. 可以先使用最小化接口验证数据库连接，再逐步调试完整功能<br>";
echo "4. 查看服务器错误日志获取更多信息";
echo "</div>";

echo "<p style='margin: 20px 0;'>";
echo "<a href='setup.html' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>测试setup.html</a>";
echo "<a href='debug_install_response.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>详细调试工具</a>";
echo "</p>";
?>