<?php
/**
 * 系统安装脚本
 * 自动化部署和初始化系统
 */

header("Content-type: text/html; charset=utf-8");

// 定义安装状态
define('INSTALL_PATH', __DIR__);
define('INSTALL_LOCK', INSTALL_PATH . '/install.lock');

class SystemInstaller {
    
    private $dbConfig = [];
    private $steps = [
        'environment' => '环境检查',
        'database' => '数据库配置',
        'initialize' => '系统初始化',
        'complete' => '安装完成'
    ];
    
    public function __construct(){
        // 检查是否已安装
        if(file_exists(INSTALL_LOCK)){
            die('系统已经安装，如需重新安装请删除 install.lock 文件');
        }
    }
    
    /**
     * 环境检查
     */
    public function checkEnvironment(){
        $checks = [];
        
        // PHP版本检查
        $phpVersion = PHP_VERSION;
        $checks['php_version'] = [
            'name' => 'PHP版本',
            'required' => '5.4.0+',
            'current' => $phpVersion,
            'status' => version_compare($phpVersion, '5.4.0', '>=')
        ];
        
        // 扩展检查
        $extensions = ['pdo', 'pdo_mysql', 'curl', 'json', 'mbstring'];
        foreach($extensions as $ext){
            $checks["ext_{$ext}"] = [
                'name' => "{$ext} 扩展",
                'required' => '必须',
                'current' => extension_loaded($ext) ? '已安装' : '未安装',
                'status' => extension_loaded($ext)
            ];
        }
        
        // 目录权限检查
        $directories = [
            'runtime/' => '运行时目录',
            'upload/' => '上传目录',
            'config/' => '配置目录'
        ];
        
        foreach($directories as $dir => $desc){
            if(!file_exists($dir)){
                @mkdir($dir, 0755, true);
            }
            
            $checks["dir_{$dir}"] = [
                'name' => $desc,
                'required' => '可写',
                'current' => is_writable($dir) ? '可写' : '不可写',
                'status' => is_writable($dir)
            ];
        }
        
        return $checks;
    }
    
    /**
     * 数据库初始化
     */
    public function initDatabase($config){
        $this->dbConfig = $config;
        
        try {
            // 设置 PDO 选项，启用缓冲查询
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                PDO::ATTR_EMULATE_PREPARES => true
            ];
            
            $pdo = new PDO(
                "mysql:host={$config['hostname']};charset=utf8mb4",
                $config['username'],
                $config['password'],
                $options
            );
            
            // 创建数据库
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` DEFAULT CHARSET utf8mb4");
            $pdo->exec("USE `{$config['database']}`");
            
            // 分步执行初始化SQL
            $this->executeSqlFile($pdo, INSTALL_PATH . '/database_core_init.sql', '核心表结构');
            $this->executeSqlFile($pdo, INSTALL_PATH . '/database_data_init.sql', '初始数据');
            
            // 验证关键表是否创建成功
            $this->verifyTables($pdo);
            
            return ['status' => true, 'message' => '数据库初始化成功'];
            
        } catch (Exception $e) {
            return ['status' => false, 'message' => '数据库初始化失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 执行 SQL 文件
     */
    private function executeSqlFile($pdo, $sqlFile, $description = ''){
        if(!file_exists($sqlFile)){
            throw new Exception("SQL文件不存在: {$sqlFile}");
        }
        
        $sql = file_get_contents($sqlFile);
        $statements = $this->splitSqlStatements($sql);
        
        foreach($statements as $i => $statement){
            $statement = trim($statement);
            if(!empty($statement)){
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // 忽略已存在的表、索引或数据错误
                    if(!preg_match('/(already exists|duplicate|table.*exists)/i', $e->getMessage())){
                        throw new Exception("执行{$description}第" . ($i+1) . "条SQL失败: " . $e->getMessage());
                    }
                }
            }
        }
    }
    
    /**
     * 安全分割 SQL 语句
     */
    private function splitSqlStatements($sql){
        $statements = [];
        $current = '';
        $inQuotes = false;
        $quoteChar = '';
        
        for($i = 0; $i < strlen($sql); $i++){
            $char = $sql[$i];
            
            if($inQuotes){
                $current .= $char;
                if($char === $quoteChar && ($i === 0 || $sql[$i-1] !== '\\')){
                    $inQuotes = false;
                }
            } else {
                if($char === '"' || $char === "'"){
                    $inQuotes = true;
                    $quoteChar = $char;
                    $current .= $char;
                } elseif($char === ';'){
                    $statement = trim($current);
                    if(!empty($statement) && !preg_match('/^(--|\/\*|\s*$)/', $statement)){
                        $statements[] = $statement;
                    }
                    $current = '';
                } else {
                    $current .= $char;
                }
            }
        }
        
        // 处理最后一个语句
        $statement = trim($current);
        if(!empty($statement) && !preg_match('/^(--|\/\*|\s*$)/', $statement)){
            $statements[] = $statement;
        }
        
        return $statements;
    }
    
    /**
     * 验证关键表是否创建成功
     */
    private function verifyTables($pdo){
        $requiredTables = [
            'web_users',
            'web_substation', 
            'web_distribution',
            'web_bill',
            'web_auth_rule'
        ];
        
        foreach($requiredTables as $table){
            $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            if(!$stmt->fetch()){
                throw new Exception("关键表 {$table} 创建失败");
            }
        }
    }
    
    /**
     * 生成数据库配置文件
     */
    public function generateDbConfig($config){
        $configContent = "<?php\nreturn [\n";
        $configContent .= "    'type'            => 'mysql',\n";
        $configContent .= "    'hostname'        => '{$config['hostname']}',\n";
        $configContent .= "    'database'        => '{$config['database']}',\n";
        $configContent .= "    'username'        => '{$config['username']}',\n";
        $configContent .= "    'password'        => '{$config['password']}',\n";
        $configContent .= "    'hostport'        => '{$config['hostport']}',\n";
        $configContent .= "    'dsn'             => '',\n";
        $configContent .= "    'params'          => [],\n";
        $configContent .= "    'charset'         => 'utf8mb4',\n";
        $configContent .= "    'prefix'          => 'web_',\n";
        $configContent .= "    'debug'           => false,\n";
        $configContent .= "    'deploy'          => 0,\n";
        $configContent .= "    'rw_separate'     => false,\n";
        $configContent .= "    'master_num'      => 1,\n";
        $configContent .= "    'slave_no'        => '',\n";
        $configContent .= "    'read_master'     => false,\n";
        $configContent .= "    'fields_strict'   => true,\n";
        $configContent .= "    'resultset_type'  => 'array',\n";
        $configContent .= "    'auto_timestamp'  => false,\n";
        $configContent .= "    'datetime_format' => 'Y-m-d H:i:s',\n";
        $configContent .= "    'sql_explain'     => false,\n";
        $configContent .= "];\n";
        
        return file_put_contents('config/database.php', $configContent);
    }
    
    /**
     * 生成IP白名单配置
     */
    public function generateIpConfig(){
        // 自动获取当前访问的域名/IP
        $currentHost = $_SERVER['HTTP_HOST'] ?? 'localhost';
        
        $ipConfigContent = "<?php\n// IP白名单配置\nreturn [\n";
        $ipConfigContent .= "    'onoff' => false, // 默认关闭IP白名单，避免安装后无法访问\n";
        $ipConfigContent .= "    'ip' => \"{$currentHost}|localhost|127.0.0.1\",\n";
        $ipConfigContent .= "];\n";
        
        // 确保目录存在
        if(!file_exists('config/extra')){
            mkdir('config/extra', 0755, true);
        }
        
        return file_put_contents('config/extra/ip.php', $ipConfigContent);
    }
    
    /**
     * 创建必要目录
     */
    public function createRequiredDirectories(){
        $dirs = ['runtime', 'runtime/cache', 'runtime/temp', 'runtime/log', 'upload', 'config/extra'];
        
        foreach($dirs as $dir){
            if(!file_exists($dir)){
                mkdir($dir, 0755, true);
            }
        }
        
        return true;
    }
    
    /**
     * 完成安装
     */
    public function completeInstall(){
        // 创建安装锁定文件
        $lockContent = "安装时间: " . date('Y-m-d H:i:s') . "\n";
        $lockContent .= "数据库: {$this->dbConfig['hostname']}/{$this->dbConfig['database']}\n";
        file_put_contents(INSTALL_LOCK, $lockContent);
        
        // 设置生产模式
        $configFile = 'config/config.php';
        if(file_exists($configFile)){
            $content = file_get_contents($configFile);
            $content = str_replace("'app_debug' => true", "'app_debug' => false", $content);
            file_put_contents($configFile, $content);
        }
        
        return true;
    }
}

// 安装流程处理
if($_SERVER['REQUEST_METHOD'] === 'POST'){
    $installer = new SystemInstaller();
    
    $step = $_POST['step'] ?? '';
    
    switch($step){
        case 'check_env':
            header('Content-Type: application/json');
            echo json_encode($installer->checkEnvironment());
            exit;
            
        case 'init_db':
            $dbConfig = [
                'hostname' => $_POST['hostname'] ?? 'localhost',
                'database' => $_POST['database'] ?? '',
                'username' => $_POST['username'] ?? '',
                'password' => $_POST['password'] ?? '',
                'hostport' => $_POST['hostport'] ?? '3306'
            ];
            
            $result = $installer->initDatabase($dbConfig);
            
            if($result['status']){
                $installer->generateDbConfig($dbConfig);
                $installer->generateIpConfig();
                $installer->createRequiredDirectories();
                $installer->completeInstall();
            }
            
            header('Content-Type: application/json');
            echo json_encode($result);
            exit;
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统安装向导</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #ddd; }
        .step.active { border-color: #007cff; background: #f0f8ff; }
        .step.completed { border-color: #28a745; background: #f0fff4; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; background: #007cff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .check-item { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .check-ok { background: #d4edda; color: #155724; }
        .check-warning { background: #fff3cd; color: #856404; }
        .check-error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>分销管理系统 - 安装向导</h1>
        
        <div id="step-environment" class="step active">
            <h3>1. 环境检查</h3>
            <div id="env-results"></div>
            <button class="btn" onclick="checkEnvironment()">检查环境</button>
        </div>
        
        <div id="step-database" class="step">
            <h3>2. 数据库配置</h3>
            <div class="form-group">
                <label>数据库主机:</label>
                <input type="text" id="hostname" value="localhost">
            </div>
            <div class="form-group">
                <label>数据库端口:</label>
                <input type="text" id="hostport" value="3306">
            </div>
            <div class="form-group">
                <label>数据库名:</label>
                <input type="text" id="database" placeholder="请输入数据库名">
            </div>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="username" placeholder="请输入数据库用户名">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" placeholder="请输入数据库密码">
            </div>
            <button class="btn" onclick="initDatabase()">初始化数据库</button>
        </div>
        
        <div id="step-complete" class="step">
            <h3>3. 安装完成</h3>
            <div id="install-results"></div>
        </div>
    </div>

    <script>
    function checkEnvironment(){
        fetch('install.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: 'step=check_env'
        })
        .then(response => response.json())
        .then(data => {
            let html = '';
            let allOk = true;
            
            for(let key in data){
                let item = data[key];
                let cssClass = item.status ? 'check-ok' : 'check-error';
                if(!item.status) allOk = false;
                
                html += `<div class="check-item ${cssClass}">
                    <strong>${item.name}:</strong> ${item.current} 
                    <span style="color: #666;">(要求: ${item.required})</span>
                </div>`;
            }
            
            document.getElementById('env-results').innerHTML = html;
            
            if(allOk){
                document.getElementById('step-environment').className = 'step completed';
                document.getElementById('step-database').className = 'step active';
            }
        });
    }
    
    function initDatabase(){
        const config = {
            step: 'init_db',
            hostname: document.getElementById('hostname').value,
            hostport: document.getElementById('hostport').value,
            database: document.getElementById('database').value,
            username: document.getElementById('username').value,
            password: document.getElementById('password').value
        };
        
        fetch('install.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: Object.keys(config).map(key => key + '=' + encodeURIComponent(config[key])).join('&')
        })
        .then(response => response.json())
        .then(data => {
            if(data.status){
                document.getElementById('step-database').className = 'step completed';
                document.getElementById('step-complete').className = 'step completed';
                document.getElementById('install-results').innerHTML = `
                    <div class="check-item check-ok">
                        <h4>安装成功！</h4>
                        <p>默认管理员账号: <strong>admin</strong></p>
                        <p>默认管理员密码: <strong>admin123456</strong></p>
                        <p style="color: red;">⚠️ 请立即登录系统修改默认密码！</p>
                        <p style="color: blue;">ℹ️ IP白名单已自动关闭，生产环境请手动启用</p>
                        <p><a href="index.php" class="btn">进入系统</a></p>
                    </div>
                `;
            } else {
                document.getElementById('install-results').innerHTML = `
                    <div class="check-item check-error">
                        <h4>安装失败</h4>
                        <p>${data.message}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            document.getElementById('install-results').innerHTML = `
                <div class="check-item check-error">
                    <h4>安装过程中发生错误</h4>
                    <p>${error.message}</p>
                </div>
            `;
        });
    }
    
    // 自动检查环境
    window.onload = function(){
        checkEnvironment();
    };
    </script>
</body>
</html>