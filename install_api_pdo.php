<?php
/**
 * 使用PDO的安装API端点
 * 解决mysqli扩展缺失问题
 */

// === 严格的输出控制 ===
ob_start();
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// === 定义常量 ===
define('INSTALL_LOCK', __DIR__ . '/install.lock');

// === 函数定义 ===
function sendJsonResponse($data) {
    // 清理任何已有输出
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    // 设置响应头
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache');
    
    // 输出JSON并退出
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

function createDatabase($config) {
    // 验证参数
    if (empty($config['hostname']) || empty($config['database']) || empty($config['username'])) {
        return ['status' => false, 'message' => '数据库配置信息不完整'];
    }
    
    try {
        // 使用PDO连接数据库
        $dsn = "mysql:host={$config['hostname']};port=" . intval($config['hostport'] ?? 3306) . ";charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
        
        // 创建数据库
        $sql = "CREATE DATABASE IF NOT EXISTS `{$config['database']}` DEFAULT CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci";
        $pdo->exec($sql);
        
        // 选择数据库
        $pdo->exec("USE `{$config['database']}`");
        
        // 创建核心表
        $coreTables = [
            // 用户表
            "CREATE TABLE IF NOT EXISTS `web_users` (
              `u_id` int(11) NOT NULL AUTO_INCREMENT,
              `u_phone` varchar(20) NOT NULL,
              `u_password` varchar(64) NOT NULL,
              `u_nickname` varchar(50) DEFAULT '',
              `u_status` tinyint(1) DEFAULT 1,
              `u_supermanage` tinyint(1) DEFAULT 1,
              `u_count` int(11) DEFAULT 0,
              `u_regtime` datetime DEFAULT NULL,
              `u_this_time` datetime DEFAULT NULL,
              `u_this_ip` varchar(50) DEFAULT '',
              `u_last_time` datetime DEFAULT NULL,
              `u_last_ip` varchar(50) DEFAULT '',
              PRIMARY KEY (`u_id`),
              UNIQUE KEY `uk_phone` (`u_phone`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
            
            // 权限规则表
            "CREATE TABLE IF NOT EXISTS `web_auth_rule` (
              `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
              `name` char(80) NOT NULL DEFAULT '',
              `title` char(20) NOT NULL DEFAULT '',
              `type` tinyint(1) NOT NULL DEFAULT 1,
              `status` tinyint(1) NOT NULL DEFAULT 1,
              `condition` char(100) NOT NULL DEFAULT '',
              `said` int(11) DEFAULT 0,
              `sort` int(11) DEFAULT 0,
              PRIMARY KEY (`id`),
              UNIQUE KEY `name` (`name`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
            
            // 权限分组表
            "CREATE TABLE IF NOT EXISTS `web_auth_group` (
              `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
              `title` char(100) NOT NULL DEFAULT '',
              `status` tinyint(1) NOT NULL DEFAULT 1,
              `rules` char(80) NOT NULL DEFAULT '',
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
        ];
        
        // 执行表创建
        $createdTables = 0;
        foreach ($coreTables as $sql) {
            try {
                $pdo->exec($sql);
                $createdTables++;
            } catch (PDOException $e) {
                if (!preg_match('/(already exists|duplicate)/i', $e->getMessage())) {
                    return ['status' => false, 'message' => '创建表失败: ' . $e->getMessage()];
                }
                $createdTables++;
            }
        }
        
        // 插入管理员账号
        $adminSql = "INSERT IGNORE INTO `web_users` 
                    (`u_phone`, `u_password`, `u_nickname`, `u_status`, `u_supermanage`, `u_regtime`) 
                    VALUES ('admin', MD5('admin123456'), '系统管理员', 1, 2, NOW())";
        $pdo->exec($adminSql);
        
        // 生成配置文件
        $dbConfigContent = "<?php\nreturn [\n";
        $dbConfigContent .= "    'type' => 'mysql',\n";
        $dbConfigContent .= "    'hostname' => '{$config['hostname']}',\n";
        $dbConfigContent .= "    'database' => '{$config['database']}',\n";
        $dbConfigContent .= "    'username' => '{$config['username']}',\n";
        $dbConfigContent .= "    'password' => '{$config['password']}',\n";
        $dbConfigContent .= "    'hostport' => '{$config['hostport']}',\n";
        $dbConfigContent .= "    'prefix' => 'web_',\n";
        $dbConfigContent .= "    'charset' => 'utf8mb4',\n";
        $dbConfigContent .= "];\n";
        
        // 确保配置目录存在
        if (!file_exists('config')) {
            @mkdir('config', 0755, true);
        }
        
        file_put_contents('config/database.php', $dbConfigContent);
        
        // 生成IP白名单配置
        if (!file_exists('config/extra')) {
            @mkdir('config/extra', 0755, true);
        }
        
        $ipConfig = "<?php\nreturn [\n    'onoff' => false,\n    'ip' => 'localhost|127.0.0.1',\n];\n";
        file_put_contents('config/extra/ip.php', $ipConfig);
        
        // 创建必要目录
        $dirs = ['runtime', 'runtime/cache', 'runtime/temp', 'runtime/log', 'upload'];
        foreach ($dirs as $dir) {
            if (!file_exists($dir)) {
                @mkdir($dir, 0755, true);
            }
        }
        
        // 创建安装锁
        file_put_contents(INSTALL_LOCK, "安装时间: " . date('Y-m-d H:i:s'));
        
        return [
            'status' => true, 
            'message' => '安装成功！已创建' . $createdTables . '个核心表，管理员账号：admin/admin123456'
        ];
        
    } catch (PDOException $e) {
        return ['status' => false, 'message' => '数据库操作失败: ' . $e->getMessage()];
    } catch (Exception $e) {
        return ['status' => false, 'message' => '安装异常: ' . $e->getMessage()];
    }
}

// === 主逻辑 ===
try {
    // 只处理POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendJsonResponse(['status' => false, 'message' => '只接受POST请求']);
    }
    
    // 检查是否已安装
    if (file_exists(INSTALL_LOCK)) {
        sendJsonResponse(['status' => false, 'message' => '系统已安装，请删除install.lock文件后重试']);
    }
    
    // 获取并验证参数
    $step = $_POST['step'] ?? '';
    if ($step !== 'install') {
        sendJsonResponse(['status' => false, 'message' => '无效的安装步骤']);
    }
    
    $config = [
        'hostname' => trim($_POST['hostname'] ?? ''),
        'database' => trim($_POST['database'] ?? ''),
        'username' => trim($_POST['username'] ?? ''),
        'password' => $_POST['password'] ?? '',
        'hostport' => trim($_POST['hostport'] ?? '3306')
    ];
    
    // 执行数据库创建
    $result = createDatabase($config);
    sendJsonResponse($result);
    
} catch (Exception $e) {
    sendJsonResponse(['status' => false, 'message' => '系统异常: ' . $e->getMessage()]);
} catch (Throwable $e) {
    sendJsonResponse(['status' => false, 'message' => '未知错误: ' . $e->getMessage()]);
}
