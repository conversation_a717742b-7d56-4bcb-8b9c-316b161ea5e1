<?php
/**
 * 批量修复AJAX检查问题的脚本
 */

function fixAjaxInFile($filepath) {
    if (!file_exists($filepath)) {
        return false;
    }
    
    $content = file_get_contents($filepath);
    $originalContent = $content;
    
    // 修复 isAjax() 检查
    $patterns = [
        // 修复简单的 isAjax 检查，改为 isPost
        '/if\s*\(\s*Request::instance\(\)->isAjax\(\)\s*\)\s*\{/' => 'if(Request::instance()->isPost()){',
        
        // 为返回 JSON 的方法添加 Content-Type 头
        '/(\s+)(return _Json\(\$res\);)/' => '$1header(\'Content-Type: application/json\');' . "\n" . '$1$2',
    ];
    
    foreach ($patterns as $pattern => $replacement) {
        $content = preg_replace($pattern, $replacement, $content);
    }
    
    // 如果内容有变化，保存文件
    if ($content !== $originalContent) {
        file_put_contents($filepath, $content);
        return true;
    }
    
    return false;
}

// 需要修复的控制器文件列表
$controllers = [
    'application/website/controller/Voicelist.php',
    'application/website/controller/Tixian.php',
    'application/website/controller/Substationgroup.php',
    'application/website/controller/Group.php',
    'application/website/controller/Substation.php',
    'application/website/controller/Index.php', // 已手动修复，再次检查
];

echo "开始批量修复AJAX问题...\n";

foreach ($controllers as $controller) {
    $fullPath = __DIR__ . '/' . $controller;
    if (fixAjaxInFile($fullPath)) {
        echo "✓ 修复: $controller\n";
    } else {
        echo "- 跳过: $controller (无需修改或文件不存在)\n";
    }
}

echo "批量修复完成！\n";
?>