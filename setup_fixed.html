<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>分销管理系统 - 安装向导 (修复版)</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .installer { width: 100%; max-width: 600px; background: white; border-radius: 12px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 30px; text-align: center; }
        .header h1 { font-size: 28px; margin-bottom: 8px; }
        .header p { opacity: 0.9; font-size: 16px; }
        .content { padding: 40px; }
        .step { margin-bottom: 30px; }
        .step h3 { color: #333; margin-bottom: 20px; font-size: 20px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #555; }
        .form-group input { width: 100%; padding: 12px 16px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 16px; transition: border-color 0.3s; }
        .form-group input:focus { outline: none; border-color: #667eea; }
        .form-group small { color: #666; font-size: 12px; margin-top: 4px; display: block; }
        .btn { width: 100%; padding: 14px; background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: transform 0.2s; }
        .btn:hover { transform: translateY(-2px); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .btn-secondary { background: #6c757d; }
        .alert { padding: 16px; border-radius: 8px; margin-bottom: 20px; }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .loading { display: none; text-align: center; padding: 20px; }
        .loading::after { content: ''; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite; display: inline-block; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .step.hidden { display: none; }
        .success-info { background: #f8f9fa; border-radius: 8px; padding: 20px; }
        .success-info h4 { color: #28a745; margin-bottom: 10px; }
        .success-info p { margin-bottom: 8px; }
        .success-info .credentials { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px; margin: 10px 0; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; font-size: 14px; }
        .test-success { background: #d4edda; color: #155724; }
        .test-error { background: #f8d7da; color: #721c24; }
        .btn-group { display: flex; gap: 10px; }
        .btn-group .btn { flex: 1; }
    </style>
</head>
<body>
    <div class="installer">
        <div class="header">
            <h1>分销管理系统</h1>
            <p>欢迎使用系统安装向导 (修复版)</p>
        </div>
        
        <div class="content">
            <!-- 数据库配置步骤 -->
            <div id="step-database" class="step">
                <h3>数据库配置</h3>
                <div id="error-message"></div>
                
                <div class="alert alert-info">
                    <strong>提示：</strong> 请确保MySQL服务已启动，并填写正确的数据库连接信息。
                </div>
                
                <form id="db-form">
                    <div class="form-group">
                        <label>数据库主机:</label>
                        <input type="text" id="hostname" value="localhost" required>
                        <small>通常为 localhost 或 127.0.0.1</small>
                    </div>
                    <div class="form-group">
                        <label>数据库端口:</label>
                        <input type="text" id="hostport" value="3306" required>
                        <small>MySQL默认端口为3306</small>
                    </div>
                    <div class="form-group">
                        <label>数据库名:</label>
                        <input type="text" id="database" placeholder="请输入数据库名" required>
                        <small>如果数据库不存在，系统会自动创建</small>
                    </div>
                    <div class="form-group">
                        <label>用户名:</label>
                        <input type="text" id="username" value="root" placeholder="请输入数据库用户名" required>
                        <small>通常为 root</small>
                    </div>
                    <div class="form-group">
                        <label>密码:</label>
                        <input type="password" id="password" placeholder="请输入数据库密码">
                        <small>如果没有密码请留空</small>
                    </div>
                    
                    <div class="btn-group">
                        <button type="button" class="btn btn-secondary" onclick="testConnection()">测试连接</button>
                        <button type="button" class="btn" onclick="installSystem()">开始安装</button>
                    </div>
                </form>
                
                <div id="test-result"></div>
            </div>
            
            <!-- 加载中 -->
            <div id="loading" class="loading"></div>
            
            <!-- 安装完成 -->
            <div id="step-complete" class="step hidden">
                <h3>安装完成</h3>
                <div class="success-info">
                    <h4>🎉 系统安装成功！</h4>
                    <p>数据库初始化完成，您现在可以使用以下账号登录系统：</p>
                    
                    <div class="credentials">
                        <p><strong>管理员账号：</strong> admin</p>
                        <p><strong>管理员密码：</strong> admin123456</p>
                    </div>
                    
                    <div class="alert alert-info">
                        <p><strong>⚠️ 重要提示：</strong></p>
                        <p>1. 请立即登录系统修改默认密码</p>
                        <p>2. IP白名单已自动关闭，生产环境请手动启用并配置</p>
                        <p>3. 建议删除本安装文件以确保安全</p>
                        <p>4. 配置生产环境时请关闭调试模式</p>
                    </div>
                    
                    <button class="btn" onclick="window.location.href='index.php'">进入系统</button>
                </div>
            </div>
        </div>
    </div>

    <script>
    function testConnection(){
        const form = document.getElementById('db-form');
        const resultDiv = document.getElementById('test-result');
        
        // 验证表单
        if(!form.checkValidity()){
            showError('请填写所有必填字段');
            return;
        }
        
        const config = {
            step: 'test',
            hostname: document.getElementById('hostname').value,
            hostport: document.getElementById('hostport').value,
            database: document.getElementById('database').value,
            username: document.getElementById('username').value,
            password: document.getElementById('password').value
        };
        
        resultDiv.innerHTML = '<div class="test-result">正在测试连接...</div>';
        
        fetch('test_connection_api.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: Object.keys(config).map(key => key + '=' + encodeURIComponent(config[key])).join('&')
        })
        .then(response => response.json())
        .then(data => {
            if(data.status){
                resultDiv.innerHTML = `<div class="test-result test-success">✅ ${data.message}</div>`;
            } else {
                resultDiv.innerHTML = `<div class="test-result test-error">❌ ${data.message}</div>`;
            }
        })
        .catch(error => {
            resultDiv.innerHTML = `<div class="test-result test-error">❌ 测试失败: ${error.message}</div>`;
        });
    }
    
    function installSystem(){
        const form = document.getElementById('db-form');
        const btn = form.querySelector('.btn:not(.btn-secondary)');
        const loading = document.getElementById('loading');
        const errorDiv = document.getElementById('error-message');
        
        // 验证表单
        if(!form.checkValidity()){
            showError('请填写所有必填字段');
            return;
        }
        
        // 显示加载状态
        document.getElementById('step-database').style.display = 'none';
        loading.style.display = 'block';
        errorDiv.innerHTML = '';
        
        const config = {
            step: 'install',
            hostname: document.getElementById('hostname').value,
            hostport: document.getElementById('hostport').value,
            database: document.getElementById('database').value,
            username: document.getElementById('username').value,
            password: document.getElementById('password').value
        };
        
        fetch('install_api_pdo.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: Object.keys(config).map(key => key + '=' + encodeURIComponent(config[key])).join('&')
        })
        .then(response => {
            if(!response.ok){
                throw new Error('网络请求失败');
            }
            return response.json();
        })
        .then(data => {
            loading.style.display = 'none';
            
            if(data.status){
                document.getElementById('step-complete').className = 'step';
            } else {
                document.getElementById('step-database').style.display = 'block';
                showError(data.message);
            }
        })
        .catch(error => {
            loading.style.display = 'none';
            document.getElementById('step-database').style.display = 'block';
            showError('安装过程中发生错误: ' + error.message);
        });
    }
    
    function showError(message){
        document.getElementById('error-message').innerHTML = 
            `<div class="alert alert-danger">${message}</div>`;
    }
    </script>
</body>
</html>
