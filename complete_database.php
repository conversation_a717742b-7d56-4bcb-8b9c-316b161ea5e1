<?php
/**
 * 数据库表结构补全脚本
 */

header("Content-type: text/html; charset=utf-8");
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>数据库表结构补全</h1>";

try {
    // 连接数据库
    $dbConfig = include 'config/database.php';
    $pdo = new PDO(
        "mysql:host={$dbConfig['hostname']};dbname={$dbConfig['database']};charset=utf8mb4",
        $dbConfig['username'],
        $dbConfig['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "✓ 数据库连接成功<br>";
    
    // 检查当前表数量
    $stmt = $pdo->query("SHOW TABLES LIKE 'web_%'");
    $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $currentCount = count($existingTables);
    
    echo "<h2>当前状态</h2>";
    echo "现有表数量: <strong>$currentCount</strong><br>";
    echo "现有表列表:<br>";
    foreach ($existingTables as $table) {
        echo "- $table<br>";
    }
    
    // 应该存在的表列表
    $expectedTables = [
        'web_users', 'web_users_logs', 'web_auth_rule', 'web_auth_group', 'web_auth_group_access',
        'web_substation', 'web_substationgroup', 'web_distribution', 'web_distributiongroup',
        'web_wxgroup', 'web_wxgrouptmp', 'web_bill', 'web_paylist', 'web_dianka', 'web_diankalog',
        'web_chouyong', 'web_duizhang', 'web_distributiontixian', 'web_substationtixian',
        'web_navigat', 'web_navigat_group', 'web_voicelist', 'web_system_config',
        'web_operation_logs', 'web_system_monitor'
    ];
    
    $expectedCount = count($expectedTables);
    $missingTables = array_diff($expectedTables, $existingTables);
    $missingCount = count($missingTables);
    
    echo "<h2>分析结果</h2>";
    echo "应有表数量: <strong>$expectedCount</strong><br>";
    echo "缺失表数量: <strong style='color: red;'>$missingCount</strong><br>";
    
    if ($missingCount > 0) {
        echo "<h3>缺失的表:</h3>";
        foreach ($missingTables as $table) {
            echo "<span style='color: red;'>- $table</span><br>";
        }
        
        echo "<h2>开始补全数据库表</h2>";
        
        // 读取并执行完整的数据库脚本
        $sqlFile = 'database_complete_init.sql';
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            
            // 分割SQL语句
            $statements = explode(';', $sql);
            $createdTables = 0;
            $errors = 0;
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (empty($statement) || strpos($statement, 'SET') === 0 || strpos($statement, '--') === 0) {
                    continue;
                }
                
                try {
                    $pdo->exec($statement);
                    if (strpos($statement, 'CREATE TABLE') !== false) {
                        preg_match('/CREATE TABLE.*?`(web_\w+)`/i', $statement, $matches);
                        if (isset($matches[1])) {
                            echo "✓ 创建表: {$matches[1]}<br>";
                            $createdTables++;
                        }
                    }
                } catch (PDOException $e) {
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "<span style='color: orange;'>! SQL错误: " . $e->getMessage() . "</span><br>";
                        $errors++;
                    }
                }
            }
            
            echo "<h3>执行结果</h3>";
            echo "处理的表: $createdTables<br>";
            echo "错误数量: $errors<br>";
            
        } else {
            echo "<span style='color: red;'>错误: 找不到 database_complete_init.sql 文件</span><br>";
        }
        
        // 重新检查表数量
        $stmt = $pdo->query("SHOW TABLES LIKE 'web_%'");
        $finalTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $finalCount = count($finalTables);
        
        echo "<h2>补全后状态</h2>";
        echo "最终表数量: <strong>$finalCount</strong><br>";
        
        if ($finalCount >= $expectedCount) {
            echo "<span style='color: green; font-size: 18px;'>✅ 数据库表结构补全成功！</span><br>";
        } else {
            echo "<span style='color: orange;'>⚠️ 部分表可能未创建成功，请检查错误信息</span><br>";
        }
        
    } else {
        echo "<span style='color: green; font-size: 18px;'>✅ 数据库表结构完整，无需补全</span><br>";
    }
    
    // 插入基础数据
    echo "<h2>检查基础数据</h2>";
    
    // 检查管理员账户
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM web_users WHERE u_phone = 'admin'");
    $stmt->execute();
    $adminExists = $stmt->fetchColumn();
    
    if ($adminExists == 0) {
        echo "正在创建管理员账户...<br>";
        $stmt = $pdo->prepare("INSERT INTO web_users (u_phone, u_password, u_nickname, u_status, u_supermanage, u_regtime) VALUES ('admin', MD5('admin123456'), '系统管理员', 1, 2, NOW())");
        $stmt->execute();
        echo "✓ 管理员账户创建成功<br>";
    } else {
        echo "✓ 管理员账户已存在<br>";
    }
    
    // 检查权限数据
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM web_auth_rule");
    $stmt->execute();
    $ruleCount = $stmt->fetchColumn();
    
    if ($ruleCount == 0) {
        echo "正在初始化权限数据...<br>";
        $rules = [
            "INSERT IGNORE INTO web_auth_rule (name, title, type, status, said, sort) VALUES 
             ('index', '首页管理', 1, 1, 0, 1),
             ('users', '用户管理', 1, 1, 0, 2),
             ('substation', '分站管理', 1, 1, 0, 3),
             ('distribution', '分销管理', 1, 1, 0, 4),
             ('wxgroup', '群组管理', 1, 1, 0, 5),
             ('bill', '账单管理', 1, 1, 0, 6),
             ('system', '系统设置', 1, 1, 0, 7)",
            
            "INSERT IGNORE INTO web_auth_group (title, status, rules) VALUES ('超级管理员', 1, '1,2,3,4,5,6,7')",
            
            "INSERT IGNORE INTO web_system_config (config_key, config_value, config_desc) VALUES 
             ('site_name', '分销管理系统', '网站名称'),
             ('default_page_size', '15', '默认分页大小')"
        ];
        
        foreach ($rules as $sql) {
            try {
                $pdo->exec($sql);
                echo "✓ 权限数据初始化<br>";
            } catch (PDOException $e) {
                echo "! 权限数据初始化警告: " . $e->getMessage() . "<br>";
            }
        }
    } else {
        echo "✓ 权限数据已存在 ($ruleCount 条规则)<br>";
    }
    
    echo "<hr>";
    echo "<h2>数据库补全完成！</h2>";
    echo "<p><strong>建议操作：</strong></p>";
    echo "<p>1. 立即登录系统测试功能</p>";
    echo "<p>2. 检查各个模块是否正常工作</p>";
    echo "<p>3. 如发现问题，请查看错误日志</p>";
    
} catch (Exception $e) {
    echo "<div style='color: red; border: 1px solid red; padding: 10px; margin: 10px 0;'>";
    echo "<strong>错误:</strong> " . $e->getMessage() . "<br>";
    echo "<strong>文件:</strong> " . $e->getFile() . ":" . $e->getLine() . "<br>";
    echo "</div>";
}
?>