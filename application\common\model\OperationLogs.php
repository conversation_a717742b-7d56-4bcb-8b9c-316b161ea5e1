<?php
namespace app\common\model;
use think\Db;
use think\Request;

class OperationLogs extends Base{
	
    protected $name = 'operation_logs';
    
    /**
     * 记录操作日志
     */
    public static function record($userType, $userId, $action = '', $params = []){
        $request = Request::instance();
        
        $logData = [
            'ol_user_type'   => $userType,
            'ol_user_id'     => $userId,
            'ol_module'      => $request->module(),
            'ol_controller'  => $request->controller(),
            'ol_action'      => $action ?: $request->action(),
            'ol_method'      => $request->method(),
            'ol_params'      => json_encode($params),
            'ol_ip'          => $request->ip(),
            'ol_user_agent'  => $request->header('User-Agent'),
            'ol_addtime'     => date('Y-m-d H:i:s'),
        ];
        
        try {
            return Db::name('operation_logs')->insert($logData);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    public function GetAll($page, $filters = []){
        $where = "1=1";
        
        if (!empty($filters['user_type'])) {
            $where .= " AND ol_user_type = {$filters['user_type']}";
        }
        
        if (!empty($filters['start_date'])) {
            $where .= " AND ol_addtime >= '{$filters['start_date']}'";
        }
        
        if (!empty($filters['end_date'])) {
            $where .= " AND ol_addtime <= '{$filters['end_date']}'";
        }
        
        return $this->where($where)->order("ol_id desc")->paginate($page);
    }
    
    /**
     * 清理过期日志
     */
    public function cleanExpiredLogs($days = 90){
        $expireDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        return $this->where("ol_addtime < '{$expireDate}'")->delete();
    }
}