<?php
/**
 * 数据库连接测试工具
 */

header("Content-type: text/html; charset=utf-8");

echo "<h1>数据库连接测试</h1>";

// 从现有配置文件读取数据库配置
$dbConfig = null;
if (file_exists('config/database.php')) {
    $dbConfig = include 'config/database.php';
    echo "<h2>当前数据库配置</h2>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><td><strong>主机</strong></td><td>" . htmlspecialchars($dbConfig['hostname']) . "</td></tr>";
    echo "<tr><td><strong>数据库</strong></td><td>" . htmlspecialchars($dbConfig['database']) . "</td></tr>";
    echo "<tr><td><strong>用户名</strong></td><td>" . htmlspecialchars($dbConfig['username']) . "</td></tr>";
    echo "<tr><td><strong>端口</strong></td><td>" . htmlspecialchars($dbConfig['hostport'] ?? '3306') . "</td></tr>";
    echo "<tr><td><strong>字符集</strong></td><td>" . htmlspecialchars($dbConfig['charset'] ?? 'utf8') . "</td></tr>";
    echo "</table>";
}

// 测试不同的数据库连接配置
$testConfigs = [
    [
        'name' => '默认配置 (localhost, root, 无密码)',
        'hostname' => 'localhost',
        'username' => 'root',
        'password' => '',
        'hostport' => '3306'
    ],
    [
        'name' => '127.0.0.1 (root, 无密码)',
        'hostname' => '127.0.0.1',
        'username' => 'root',
        'password' => '',
        'hostport' => '3306'
    ]
];

// 如果有现有配置，也测试它
if ($dbConfig) {
    $testConfigs[] = [
        'name' => '现有配置文件',
        'hostname' => $dbConfig['hostname'],
        'username' => $dbConfig['username'],
        'password' => $dbConfig['password'],
        'hostport' => $dbConfig['hostport'] ?? '3306'
    ];
}

echo "<h2>连接测试结果</h2>";

foreach ($testConfigs as $config) {
    echo "<h3>" . htmlspecialchars($config['name']) . "</h3>";
    
    try {
        $dsn = "mysql:host={$config['hostname']};port=" . intval($config['hostport']) . ";charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_TIMEOUT => 5
        ]);
        
        // 获取MySQL版本
        $version = $pdo->query('SELECT VERSION()')->fetchColumn();
        
        // 列出数据库
        $databases = $pdo->query('SHOW DATABASES')->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 4px; color: #155724;'>";
        echo "✅ <strong>连接成功！</strong><br>";
        echo "<strong>MySQL版本:</strong> " . htmlspecialchars($version) . "<br>";
        echo "<strong>可用数据库:</strong> " . implode(', ', array_map('htmlspecialchars', $databases));
        echo "</div>";
        
        // 测试创建数据库权限
        $testDbName = 'test_install_' . date('YmdHis');
        try {
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$testDbName`");
            $pdo->exec("DROP DATABASE `$testDbName`");
            echo "<div style='background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 4px; color: #0c5460;'>";
            echo "✅ 具有创建/删除数据库权限";
            echo "</div>";
        } catch (PDOException $e) {
            echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 4px; color: #856404;'>";
            echo "⚠️ 无法创建数据库: " . htmlspecialchars($e->getMessage());
            echo "</div>";
        }
        
    } catch (PDOException $e) {
        echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 4px; color: #721c24;'>";
        echo "❌ <strong>连接失败:</strong> " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
}

echo "<hr>";
echo "<h2>MySQL服务状态检查</h2>";

// 检查MySQL服务是否运行
$mysqlProcesses = [];
exec('tasklist /FI "IMAGENAME eq mysqld.exe" /FO CSV', $mysqlProcesses);

if (count($mysqlProcesses) > 1) { // 第一行是标题
    echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 4px; color: #155724;'>";
    echo "✅ MySQL服务正在运行<br>";
    foreach (array_slice($mysqlProcesses, 1) as $process) {
        echo htmlspecialchars($process) . "<br>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 4px; color: #721c24;'>";
    echo "❌ 未检测到MySQL服务进程";
    echo "</div>";
}

echo "<hr>";
echo "<h2>解决方案建议</h2>";
echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 4px;'>";
echo "<h4>如果连接失败，请尝试：</h4>";
echo "<ol>";
echo "<li><strong>启动MySQL服务：</strong> 确保MySQL/MariaDB服务正在运行</li>";
echo "<li><strong>检查用户权限：</strong> 确保root用户可以无密码登录，或设置正确的密码</li>";
echo "<li><strong>检查端口：</strong> 确认MySQL运行在3306端口</li>";
echo "<li><strong>防火墙设置：</strong> 确保防火墙允许MySQL连接</li>";
echo "<li><strong>配置文件：</strong> 检查MySQL配置文件中的bind-address设置</li>";
echo "</ol>";
echo "</div>";

echo "<p><a href='setup.html' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>返回安装页面</a></p>";
?>
