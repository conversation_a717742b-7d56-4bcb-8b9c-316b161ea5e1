# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 ThinkPHP 5 框架的多模块分销管理系统，主要功能包括分站管理、分销员管理、订单处理、财务管理等。

## 架构结构

### 核心模块架构
- **website**: 主后台管理模块，包含所有核心功能
- **substation**: 分站模块，处理分站相关业务
- **group**: 群组模块，管理微信群组功能  
- **fenxiao**: 分销模块，处理分销员相关业务
- **notify**: 通知回调模块，处理各种支付回调

### 目录结构
```
├── application/          # 应用主目录
│   ├── website/         # 主后台管理模块
│   ├── substation/      # 分站管理模块
│   ├── group/           # 群组管理模块
│   ├── fenxiao/         # 分销管理模块
│   ├── notify/          # 回调通知模块
│   └── common/          # 公共模块（模型、验证器）
├── config/              # 配置文件目录
├── template/            # 前端模板资源
├── thinkphp/            # ThinkPHP 框架核心
├── runtime/             # 运行时缓存目录
└── database_optimization.sql  # 数据库性能优化脚本
```

### 核心控制器层级
所有控制器都继承自 `Base` 控制器，实现了：
- 权限认证系统 (Auth.Auth 类)
- Session 管理
- IP 白名单检查
- 统一的错误处理和响应格式

## 数据库配置

### 主要配置
- **数据库类型**: MySQL
- **表前缀**: `web_`
- **字符集**: utf8
- **连接方式**: 单一服务器模式

### 性能优化
项目包含完整的数据库优化脚本 (`database_optimization.sql`)，涵盖：
- 用户相关表索引优化
- 分站业务表索引优化
- 财务相关表索引优化
- 系统核心表索引优化

## 系统部署和维护

### 初次安装

**安装方式**: 系统提供两种安装方式：

```bash
# 方法1: HTML 安装向导（推荐，用户友好）
http://your-domain/setup.html

# 方法2: PHP 安装脚本（解决 PDO 缓冲问题）
http://your-domain/simple_install.php

# 方法3: 命令行安装
mysql -u username -p database_name < database_complete_init.sql
```

**安装特性**:
- **PDO 问题解决**: `simple_install.php` 使用 mysqli 避免缓冲查询问题
- **IP白名单处理**: 安装时自动关闭，防止安装后无法访问
- **目录权限**: 自动创建并检查 `config/`, `runtime/`, `upload/` 目录权限
- **数据库初始化**: 使用 `database_complete_init.sql` 一次性创建所有表结构
- **默认管理员**: admin/admin123456（安装后请立即修改密码）

### 常用开发命令

#### PHP 相关
```bash
# 启动内置开发服务器
php -S localhost:8000 -t .

# 清除 ThinkPHP 缓存
rm -rf runtime/cache/*
rm -rf runtime/temp/*

# 数据库优化（需要 MySQL 连接）
mysql -u username -p database_name < database_optimization.sql
```

#### 系统维护命令
```bash
# 数据库备份
php scripts/deploy.php backup

# 清理系统缓存
php scripts/deploy.php cache

# 数据库优化
php scripts/deploy.php optimize

# 系统健康检查
php scripts/deploy.php health
```

### 代码结构规范
- 控制器位置：`application/{模块}/controller/`
- 模型位置：`application/common/model/`
- 验证器位置：`application/common/validate/`
- 模板位置：`template/{模块}/`

## 开发注意事项

### 安全配置
- 项目启用了 IP 白名单检查机制
- 所有 AJAX 请求都需要权限验证
- Session 管理集成在 Base 控制器中

### 权限系统
- 基于 `Auth.Auth` 类的 RBAC 权限控制
- 白名单路由 (`UrlNoPower`) 无需登录验证
- 公共操作 (`ActionNoPower`) 登录后无需权限验证

### 数据库表命名规范
- 所有表使用 `web_` 前缀
- 字段命名采用下划线分割
- 主键通常为 `{表名简写}_id` 格式

### 响应格式
使用 `_Json()` 函数统一返回 JSON 格式：
```php
return _Json([
    'status' => 'success|error',
    'msg' => '提示信息',
    'data' => $data
]);
```

### 模板引擎
- 使用 ThinkPHP 内置模板引擎
- 模板标签：`{}` 包围
- 默认模板后缀：`.html`
- 集成 Layui 前端框架

## 新增功能特性

### 安全增强 ✅
- **操作日志记录**: 自动记录用户操作，支持安全审计
- **输入安全验证**: 防止 SQL 注入和 XSS 攻击
- **请求频率限制**: 防止暴力攻击和恶意请求
- **增强版基础控制器**: `EnhancedBase` 提供更强的安全保护

### 系统监控 ✅
- **健康检查服务**: `SystemMonitor` 提供全面的系统状态检测
- **性能监控**: 数据库连接、磁盘空间、缓存状态监控
- **自动化报告**: 生成系统运行状况报告

### 配置管理 ✅
- **统一配置系统**: `SystemConfig` 模型提供集中化配置管理
- **缓存优化**: 配置信息自动缓存，提升访问性能
- **分组管理**: 支持按功能模块分组管理配置项

### 部署工具 ✅
- **简化安装**: `simple_install.php` + `setup.html` 双重安装选择
- **PDO 问题修复**: 使用 mysqli 替代方案解决缓冲查询问题  
- **部署脚本**: `scripts/deploy.php` 支持命令行部署和维护
- **数据库管理**: `complete_database.php` 补全缺失表结构

### 系统工具 ✅
- **诊断工具**: `system_diagnosis.php` 全面系统状态检查
- **数据库优化**: `database_optimization.sql` 和 `database_complete_init.sql`
- **操作日志**: `OperationLogs` 模型提供审计功能
- **配置管理**: `SystemConfig` 模型动态配置系统

## 使用新功能

### 启用增强安全控制器
```php
// 在新控制器中继承增强版基类
use app\common\controller\EnhancedBase;

class YourController extends EnhancedBase {
    // 自动获得安全增强功能
}
```

### 使用系统配置
```php
// 获取配置值
$value = SystemConfig::getValue('config_key', 'default_value');

// 设置配置值
SystemConfig::setValue('config_key', 'new_value');
```

### 记录操作日志
```php
// 手动记录日志
OperationLogs::record($userType, $userId, $action, $params);
```

## 故障排除工具

### 系统诊断
```bash
# 全面系统检查（推荐）
http://your-domain/system_diagnosis.php

# 数据库表结构补全  
http://your-domain/complete_database.php
```

### 常见问题解决
- **登录问题**: 使用 `system_diagnosis.php` 进行全面检查
- **数据库问题**: 使用 `complete_database.php` 补全缺失表
- **权限问题**: Base控制器自动重定向到登录页面
- **配置问题**: 安装脚本自动生成所有必要配置

### 备份文件位置
如需恢复已删除的调试文件，请查看 `backup_before_optimization/` 目录。