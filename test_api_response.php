<?php
/**
 * 直接测试API响应
 */

echo "=== 测试install_api_pdo.php ===\n";

// 模拟POST请求
$_POST = [
    'step' => 'install',
    'hostname' => 'localhost',
    'database' => 'test_db',
    'username' => 'root',
    'password' => '',
    'hostport' => '3306'
];

$_SERVER['REQUEST_METHOD'] = 'POST';

// 捕获输出
ob_start();

try {
    include 'install_api_pdo.php';
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$output = ob_get_contents();
ob_end_clean();

echo "输出长度: " . strlen($output) . " bytes\n";
echo "输出内容:\n";
echo $output . "\n";

// 尝试解析JSON
$json = json_decode($output, true);
if ($json === null) {
    echo "JSON解析失败: " . json_last_error_msg() . "\n";
    echo "原始输出的十六进制:\n";
    echo bin2hex($output) . "\n";
} else {
    echo "JSON解析成功:\n";
    print_r($json);
}
?>
