<?php
/**
 * 系统全面诊断脚本
 * 检查各种潜在问题
 */

header("Content-type: text/html; charset=utf-8");
echo "<h1>系统全面诊断</h1>";

// 1. 数据库连接检查
echo "<h2>1. 数据库连接检查</h2>";
try {
    $dbConfig = include 'config/database.php';
    $pdo = new PDO(
        "mysql:host={$dbConfig['hostname']};dbname={$dbConfig['database']};charset=utf8mb4",
        $dbConfig['username'],
        $dbConfig['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    echo "✓ 数据库连接正常<br>";
    
    // 检查关键表
    $tables = ['web_users', 'web_auth_rule', 'web_auth_group'];
    foreach($tables as $table){
        $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
        $count = $stmt->fetchColumn();
        echo "✓ {$table}: {$count} 条记录<br>";
    }
    
} catch (Exception $e) {
    echo "✗ 数据库连接失败: " . $e->getMessage() . "<br>";
}

// 2. 配置文件检查
echo "<h2>2. 配置文件检查</h2>";
$configFiles = [
    'config/database.php' => '数据库配置',
    'config/config.php' => '应用配置',
    'config/extra/ip.php' => 'IP白名单配置',
    'config/extra/web.php' => '网站配置'
];

foreach($configFiles as $file => $desc){
    if(file_exists($file)){
        echo "✓ {$desc} ({$file})<br>";
    } else {
        echo "✗ {$desc} 缺失 ({$file})<br>";
    }
}

// 3. 目录权限检查
echo "<h2>3. 目录权限检查</h2>";
$dirs = ['runtime', 'runtime/cache', 'runtime/temp', 'runtime/log', 'upload', 'config/extra'];
foreach($dirs as $dir){
    if(!file_exists($dir)){
        @mkdir($dir, 0755, true);
    }
    
    if(is_writable($dir)){
        echo "✓ {$dir} 可写<br>";
    } else {
        echo "✗ {$dir} 不可写<br>";
    }
}

// 4. PHP环境检查
echo "<h2>4. PHP环境检查</h2>";
echo "PHP版本: " . PHP_VERSION . "<br>";
$extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'curl'];
foreach($extensions as $ext){
    if(extension_loaded($ext)){
        echo "✓ {$ext} 扩展已加载<br>";
    } else {
        echo "✗ {$ext} 扩展未加载<br>";
    }
}

// 5. 路由测试
echo "<h2>5. 路由测试</h2>";
echo "当前请求方法: " . $_SERVER['REQUEST_METHOD'] . "<br>";
echo "当前URI: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'N/A') . "<br>";

// 6. Session测试
echo "<h2>6. Session测试</h2>";
if(session_status() === PHP_SESSION_NONE){
    session_start();
}
$_SESSION['test'] = 'session_works';
if(isset($_SESSION['test'])){
    echo "✓ Session 工作正常<br>";
    unset($_SESSION['test']);
} else {
    echo "✗ Session 不工作<br>";
}

// 7. 模型测试
echo "<h2>7. 模型和验证器测试</h2>";
if(file_exists('application/common/model/Users.php')){
    echo "✓ Users模型存在<br>";
} else {
    echo "✗ Users模型缺失<br>";
}

if(file_exists('application/common/validate/Users.php')){
    echo "✓ Users验证器存在<br>";
} else {
    echo "✗ Users验证器缺失<br>";
}

// 8. ThinkPHP核心文件检查
echo "<h2>8. ThinkPHP核心文件检查</h2>";
$coreFiles = [
    'thinkphp/start.php' => 'ThinkPHP启动文件',
    'thinkphp/base.php' => 'ThinkPHP基础文件',
    'application/common.php' => '公共函数文件'
];

foreach($coreFiles as $file => $desc){
    if(file_exists($file)){
        echo "✓ {$desc}<br>";
    } else {
        echo "✗ {$desc} 缺失<br>";
    }
}

// 9. 测试登录功能
echo "<h2>9. 登录功能测试</h2>";
echo "<div id='login-test'>";
echo "<input type='text' id='test-username' value='admin' placeholder='用户名'>";
echo "<input type='password' id='test-password' value='admin123456' placeholder='密码'>";
echo "<button onclick='testLogin()'>测试登录</button>";
echo "<div id='login-result'></div>";
echo "</div>";

echo "<script>
function testLogin(){
    var username = document.getElementById('test-username').value;
    var password = document.getElementById('test-password').value;
    var resultDiv = document.getElementById('login-result');
    
    var xhr = new XMLHttpRequest();
    xhr.open('POST', '/website/index/Login', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    
    xhr.onload = function(){
        resultDiv.innerHTML = '<strong>状态:</strong> ' + xhr.status + '<br><strong>响应:</strong><pre>' + xhr.responseText + '</pre>';
    };
    
    xhr.onerror = function(){
        resultDiv.innerHTML = '<strong>请求失败</strong>';
    };
    
    xhr.send('username=' + encodeURIComponent(username) + '&password=' + encodeURIComponent(password));
}
</script>";

echo "<hr><p><strong>诊断完成!</strong> 请检查上述结果，特别关注标记为 ✗ 的问题。</p>";
?>