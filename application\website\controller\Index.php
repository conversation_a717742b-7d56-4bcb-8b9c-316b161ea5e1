<?php
namespace app\website\controller;
use app\website\controller\Base;
use think\Session;
use think\Request;

class Index extends Base{
	
	public function Index(){
		//echo md5("123456qq");
		return view();
	}
	

	public function Login(){
		// 检查是否为POST请求
		if(Request::instance()->isPost()){
			$data = [
				'username' => trim(input('username')),
				'password' => trim(input('password')),
			];
			$res = model("users")->Login($data);
			if($res['status']==1){
				$this->SetLoginSession($res['userinfo']);
			}
			
			// 统一返回JSON格式
			header('Content-Type: application/json');
			return _Json($res);
		}
		
		// GET请求显示登录页面
		return view();
	}
	
	public function OutLogin(){
		if(Request::instance()->isPost()){
			$this->SetOutLoginSession();
			$res['status'] = "success";
			$res['data']   = "退出成功！";
			header('Content-Type: application/json');
			return _Json($res);
		}
	}
	
	protected function SetLoginSession($userinfo){
		session("uid",$userinfo['u_id']);
		session("uphone",$userinfo['u_phone']);
		session("unickname",$userinfo['u_nickname']);
		//session("gid",$userinfo['g_id']);
	}
	
	protected function SetOutLoginSession(){
		session("uid",null);
		session("uphone",null);
		session("unickname",null);
		//session("gid",null);
	}
	
}