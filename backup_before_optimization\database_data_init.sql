-- ========================================
-- 系统初始数据插入脚本
-- 插入必要的基础数据
-- ========================================

SET NAMES utf8mb4;

-- 创建默认超级管理员 (密码: admin123456)
INSERT INTO `web_users` (
  `u_phone`, `u_password`, `u_nickname`, `u_status`, `u_supermanage`, `u_regtime`
) VALUES (
  'admin', 
  MD5('admin123456'), 
  '系统管理员', 
  1, 
  2, 
  NOW()
) ON DUPLICATE KEY UPDATE `u_password` = `u_password`;

-- 创建基础权限规则
INSERT INTO `web_auth_rule` (`name`, `title`, `type`, `status`, `said`, `sort`) VALUES
('index', '首页管理', 1, 1, 0, 1),
('users', '用户管理', 1, 1, 0, 2),
('substation', '分站管理', 1, 1, 0, 3),
('distribution', '分销管理', 1, 1, 0, 4),
('wxgroup', '群组管理', 1, 1, 0, 5),
('bill', '账单管理', 1, 1, 0, 6),
('finance', '财务管理', 1, 1, 0, 7),
('system', '系统设置', 1, 1, 0, 8)
ON DUPLICATE KEY UPDATE `title` = VALUES(`title`);

-- 创建超级管理员权限组
INSERT INTO `web_auth_group` (`title`, `status`, `rules`) VALUES
('超级管理员', 1, '1,2,3,4,5,6,7,8')
ON DUPLICATE KEY UPDATE `rules` = VALUES(`rules`);

-- 插入默认系统配置
INSERT INTO `web_system_config` (`config_key`, `config_value`, `config_type`, `config_group`, `config_desc`) VALUES
('site_name', '分销管理系统', 'string', 'basic', '网站名称'),
('site_status', '1', 'boolean', 'basic', '网站状态 1:开启 0:关闭'),
('default_page_size', '15', 'number', 'basic', '默认分页大小'),
('password_min_length', '6', 'number', 'security', '密码最小长度'),
('login_attempt_limit', '5', 'number', 'security', '登录尝试次数限制'),
('session_expire_time', '7200', 'number', 'security', 'Session过期时间(秒)'),
('enable_ip_whitelist', '0', 'boolean', 'security', '是否启用IP白名单'),
('log_retention_days', '90', 'number', 'system', '日志保留天数'),
('cache_expire_time', '3600', 'number', 'system', '缓存过期时间(秒)'),
('request_freq_limit', '100', 'number', 'security', '请求频率限制(每分钟)')
ON DUPLICATE KEY UPDATE `config_value` = VALUES(`config_value`);