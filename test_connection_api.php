<?php
/**
 * 数据库连接测试API
 */

// === 严格的输出控制 ===
ob_start();
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// === 函数定义 ===
function sendJsonResponse($data) {
    // 清理任何已有输出
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    // 设置响应头
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache');
    
    // 输出JSON并退出
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

function testDatabaseConnection($config) {
    // 验证参数
    if (empty($config['hostname']) || empty($config['username'])) {
        return ['status' => false, 'message' => '主机名和用户名不能为空'];
    }
    
    try {
        // 使用PDO连接数据库
        $dsn = "mysql:host={$config['hostname']};port=" . intval($config['hostport'] ?? 3306) . ";charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_TIMEOUT => 5,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
        
        // 获取MySQL版本
        $version = $pdo->query('SELECT VERSION()')->fetchColumn();
        
        // 检查是否可以创建数据库
        $canCreateDb = false;
        $dbExists = false;
        
        if (!empty($config['database'])) {
            // 检查数据库是否存在
            $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
            $stmt->execute([$config['database']]);
            $dbExists = $stmt->fetch() !== false;
            
            // 测试创建数据库权限
            if (!$dbExists) {
                try {
                    $testDbName = 'test_' . uniqid();
                    $pdo->exec("CREATE DATABASE `$testDbName`");
                    $pdo->exec("DROP DATABASE `$testDbName`");
                    $canCreateDb = true;
                } catch (PDOException $e) {
                    // 无法创建数据库
                }
            }
        }
        
        $message = "连接成功！MySQL版本: $version";
        
        if (!empty($config['database'])) {
            if ($dbExists) {
                $message .= "，数据库 '{$config['database']}' 已存在";
            } elseif ($canCreateDb) {
                $message .= "，具有创建数据库权限";
            } else {
                $message .= "，但无法创建数据库 '{$config['database']}'，请手动创建或检查权限";
            }
        }
        
        return [
            'status' => true, 
            'message' => $message,
            'details' => [
                'version' => $version,
                'database_exists' => $dbExists,
                'can_create_database' => $canCreateDb
            ]
        ];
        
    } catch (PDOException $e) {
        $errorMsg = $e->getMessage();
        
        // 提供更友好的错误信息
        if (strpos($errorMsg, 'Access denied') !== false) {
            return ['status' => false, 'message' => '数据库访问被拒绝，请检查用户名和密码'];
        } elseif (strpos($errorMsg, 'Connection refused') !== false) {
            return ['status' => false, 'message' => '无法连接到数据库服务器，请检查主机名和端口'];
        } elseif (strpos($errorMsg, 'Unknown database') !== false) {
            return ['status' => false, 'message' => '指定的数据库不存在'];
        } else {
            return ['status' => false, 'message' => '连接失败: ' . $errorMsg];
        }
    } catch (Exception $e) {
        return ['status' => false, 'message' => '测试异常: ' . $e->getMessage()];
    }
}

// === 主逻辑 ===
try {
    // 只处理POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendJsonResponse(['status' => false, 'message' => '只接受POST请求']);
    }
    
    // 获取并验证参数
    $step = $_POST['step'] ?? '';
    if ($step !== 'test') {
        sendJsonResponse(['status' => false, 'message' => '无效的测试步骤']);
    }
    
    $config = [
        'hostname' => trim($_POST['hostname'] ?? ''),
        'database' => trim($_POST['database'] ?? ''),
        'username' => trim($_POST['username'] ?? ''),
        'password' => $_POST['password'] ?? '',
        'hostport' => trim($_POST['hostport'] ?? '3306')
    ];
    
    // 执行连接测试
    $result = testDatabaseConnection($config);
    sendJsonResponse($result);
    
} catch (Exception $e) {
    sendJsonResponse(['status' => false, 'message' => '系统异常: ' . $e->getMessage()]);
} catch (Throwable $e) {
    sendJsonResponse(['status' => false, 'message' => '未知错误: ' . $e->getMessage()]);
}
