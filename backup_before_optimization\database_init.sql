-- ========================================
-- 系统初始化脚本
-- 创建完整的数据库结构和初始数据
-- ========================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 1. 核心业务表结构创建
-- ========================================

-- 用户表
CREATE TABLE IF NOT EXISTS `web_users` (
  `u_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `u_phone` varchar(20) NOT NULL COMMENT '手机号',
  `u_password` varchar(64) NOT NULL COMMENT '密码',
  `u_nickname` varchar(50) DEFAULT '' COMMENT '昵称',
  `u_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:禁用',
  `u_supermanage` tinyint(1) DEFAULT 1 COMMENT '超管状态 1:普通 2:超级管理员',
  `u_count` int(11) DEFAULT 0 COMMENT '登录次数',
  `u_regtime` datetime DEFAULT NULL COMMENT '注册时间',
  `u_this_time` datetime DEFAULT NULL COMMENT '本次登录时间',
  `u_this_ip` varchar(50) DEFAULT '' COMMENT '本次登录IP',
  `u_last_time` datetime DEFAULT NULL COMMENT '上次登录时间',
  `u_last_ip` varchar(50) DEFAULT '' COMMENT '上次登录IP',
  PRIMARY KEY (`u_id`),
  UNIQUE KEY `uk_phone` (`u_phone`),
  KEY `idx_phone_status` (`u_phone`, `u_status`),
  KEY `idx_regtime` (`u_regtime`),
  KEY `idx_last_time` (`u_last_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户登录日志表
CREATE TABLE IF NOT EXISTS `web_users_logs` (
  `ul_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `u_id` int(11) NOT NULL COMMENT '用户ID',
  `ul_type` tinyint(1) DEFAULT 1 COMMENT '操作类型 1:登录',
  `ul_addtime` datetime NOT NULL COMMENT '操作时间',
  `ul_ip` varchar(50) DEFAULT '' COMMENT '操作IP',
  PRIMARY KEY (`ul_id`),
  KEY `idx_uid_type_time` (`u_id`, `ul_type`, `ul_addtime`),
  KEY `idx_ip_time` (`ul_ip`, `ul_addtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志表';

-- 权限规则表
CREATE TABLE IF NOT EXISTS `web_auth_rule` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `name` char(80) NOT NULL DEFAULT '' COMMENT '规则唯一标识',
  `title` char(20) NOT NULL DEFAULT '' COMMENT '规则中文名称',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '规则类型',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `condition` char(100) NOT NULL DEFAULT '' COMMENT '规则表达式',
  `said` int(11) DEFAULT 0 COMMENT '上级ID',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `idx_status_said_sort` (`status`, `said`, `sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限规则表';

-- 权限分组表
CREATE TABLE IF NOT EXISTS `web_auth_group` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `title` char(100) NOT NULL DEFAULT '' COMMENT '分组名称',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `rules` char(80) NOT NULL DEFAULT '' COMMENT '规则ID',
  PRIMARY KEY (`id`),
  KEY `idx_status_title` (`status`, `title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限分组表';

-- 分站表
CREATE TABLE IF NOT EXISTS `web_substation` (
  `su_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分站ID',
  `su_s_id` int(11) DEFAULT 0 COMMENT '上级分站ID',
  `su_g_id` int(11) NOT NULL COMMENT '分站群组ID',
  `su_name` varchar(50) NOT NULL COMMENT '分站账号',
  `su_pass` varchar(50) NOT NULL COMMENT '分站密码',
  `su_title` varchar(100) DEFAULT '' COMMENT '分站名称',
  `su_domain` varchar(100) NOT NULL COMMENT '分站域名',
  `su_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:禁用 3:过期',
  `su_addtime` datetime NOT NULL COMMENT '添加时间',
  `su_endtime` date NOT NULL COMMENT '到期时间',
  `su_fz_money` decimal(10,2) DEFAULT 0.00 COMMENT '分站余额',
  `su_dk` decimal(10,2) DEFAULT 0.00 COMMENT '点卡余额',
  PRIMARY KEY (`su_id`),
  UNIQUE KEY `uk_name` (`su_name`),
  UNIQUE KEY `uk_domain` (`su_domain`),
  KEY `idx_name_status` (`su_name`, `su_status`),
  KEY `idx_addtime_status` (`su_addtime`, `su_status`),
  KEY `idx_endtime_status` (`su_endtime`, `su_status`),
  KEY `idx_parent_status_time` (`su_s_id`, `su_status`, `su_addtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分站表';

-- ========================================
-- 2. 补充缺失的表结构
-- ========================================

-- Voicelist 表 (补充缺失)
CREATE TABLE IF NOT EXISTS `web_voicelist` (
  `vol_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '语音ID',
  `vol_title` varchar(100) NOT NULL COMMENT '语音标题',
  `vol_content` text COMMENT '语音内容/描述',
  `vol_file_path` varchar(255) DEFAULT '' COMMENT '语音文件路径',
  `vol_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:禁用',
  `vol_addtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `vol_updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`vol_id`),
  KEY `idx_status_addtime` (`vol_status`, `vol_addtime`),
  KEY `idx_title` (`vol_title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='语音列表表';

-- 系统配置表 (规范化)
CREATE TABLE IF NOT EXISTS `web_system_config` (
  `config_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型',
  `config_group` varchar(30) DEFAULT 'system' COMMENT '配置分组',
  `config_desc` varchar(100) DEFAULT '' COMMENT '配置说明',
  `is_readonly` tinyint(1) DEFAULT 0 COMMENT '是否只读',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`config_id`),
  UNIQUE KEY `uk_key` (`config_key`),
  KEY `idx_group` (`config_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- ========================================
-- 3. 安全增强表结构
-- ========================================

-- 操作日志表
CREATE TABLE IF NOT EXISTS `web_operation_logs` (
  `ol_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `ol_user_type` tinyint(1) NOT NULL COMMENT '用户类型 1:管理员 2:分站 3:分销',
  `ol_user_id` int(11) NOT NULL COMMENT '用户ID',
  `ol_module` varchar(30) DEFAULT '' COMMENT '模块名',
  `ol_controller` varchar(30) DEFAULT '' COMMENT '控制器名',
  `ol_action` varchar(30) DEFAULT '' COMMENT '操作名',
  `ol_method` varchar(10) DEFAULT '' COMMENT '请求方法',
  `ol_params` text COMMENT '请求参数',
  `ol_ip` varchar(50) DEFAULT '' COMMENT '操作IP',
  `ol_user_agent` varchar(255) DEFAULT '' COMMENT '用户代理',
  `ol_addtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`ol_id`),
  KEY `idx_user_type_id_time` (`ol_user_type`, `ol_user_id`, `ol_addtime`),
  KEY `idx_action_time` (`ol_action`, `ol_addtime`),
  KEY `idx_module_controller` (`ol_module`, `ol_controller`),
  KEY `idx_ip_time` (`ol_ip`, `ol_addtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 系统监控表
CREATE TABLE IF NOT EXISTS `web_system_monitor` (
  `sm_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '监控ID',
  `sm_type` varchar(20) NOT NULL COMMENT '监控类型',
  `sm_metric` varchar(50) NOT NULL COMMENT '监控指标',
  `sm_value` decimal(15,4) DEFAULT 0.0000 COMMENT '指标值',
  `sm_threshold` decimal(15,4) DEFAULT 0.0000 COMMENT '阈值',
  `sm_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:警告 3:异常',
  `sm_message` varchar(255) DEFAULT '' COMMENT '监控消息',
  `sm_check_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '检查时间',
  PRIMARY KEY (`sm_id`),
  KEY `idx_type_time` (`sm_type`, `sm_check_time`),
  KEY `idx_status_time` (`sm_status`, `sm_check_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统监控表';

-- ========================================
-- 4. 初始化基础数据
-- ========================================

-- 创建默认超级管理员 (密码: admin123456)
INSERT INTO `web_users` (
  `u_phone`, `u_password`, `u_nickname`, `u_status`, `u_supermanage`, `u_regtime`
) VALUES (
  'admin', 
  MD5('admin123456'), 
  '系统管理员', 
  1, 
  2, 
  NOW()
) ON DUPLICATE KEY UPDATE `u_password` = `u_password`;

-- 创建基础权限规则
INSERT INTO `web_auth_rule` (`name`, `title`, `type`, `status`, `said`, `sort`) VALUES
('index', '首页管理', 1, 1, 0, 1),
('users', '用户管理', 1, 1, 0, 2),
('substation', '分站管理', 1, 1, 0, 3),
('distribution', '分销管理', 1, 1, 0, 4),
('wxgroup', '群组管理', 1, 1, 0, 5),
('bill', '账单管理', 1, 1, 0, 6),
('finance', '财务管理', 1, 1, 0, 7),
('system', '系统设置', 1, 1, 0, 8)
ON DUPLICATE KEY UPDATE `title` = VALUES(`title`);

-- 创建超级管理员权限组
INSERT INTO `web_auth_group` (`title`, `status`, `rules`) VALUES
('超级管理员', 1, '1,2,3,4,5,6,7,8')
ON DUPLICATE KEY UPDATE `rules` = VALUES(`rules`);

-- 创建默认分站群组
INSERT INTO `web_substation_group` (
  `su_g_title`, `su_g_scale`, `su_g_day`, `su_g_addtime`
) VALUES (
  '默认分站群组', 
  0.00, 
  0, 
  NOW()
) ON DUPLICATE KEY UPDATE `su_g_title` = `su_g_title`;

-- 创建默认分销群组
INSERT INTO `web_distribution_group` (
  `su_id`, `dg_title`, `dg_scale`, `dg_addtime`
) VALUES (
  0,
  '默认分销群组', 
  0.00, 
  NOW()
) ON DUPLICATE KEY UPDATE `dg_title` = `dg_title`;

-- 创建默认导航菜单
INSERT INTO `web_navigat_show` (
  `sns_id`, `ns_title`, `ns_controller`, `ns_method`, `ns_status`, `ns_sort`
) VALUES
(0, '系统管理', 'index', 'index', 1, 1),
(0, '用户管理', 'users', 'index', 1, 2),
(0, '分站管理', 'substation', 'index', 1, 3),
(0, '分销管理', 'distribution', 'index', 1, 4),
(0, '群组管理', 'wxgroup', 'index', 1, 5),
(0, '账单管理', 'bill', 'index', 1, 6),
(0, '财务管理', 'finance', 'index', 1, 7),
(0, '系统设置', 'setup', 'index', 1, 8)
ON DUPLICATE KEY UPDATE `ns_title` = VALUES(`ns_title`);

-- ========================================
-- 5. 系统配置初始化
-- ========================================

-- 插入默认系统配置
INSERT INTO `web_system_config` (`config_key`, `config_value`, `config_type`, `config_group`, `config_desc`) VALUES
('site_name', '分销管理系统', 'string', 'basic', '网站名称'),
('site_status', '1', 'boolean', 'basic', '网站状态 1:开启 0:关闭'),
('default_page_size', '15', 'number', 'basic', '默认分页大小'),
('password_min_length', '6', 'number', 'security', '密码最小长度'),
('login_attempt_limit', '5', 'number', 'security', '登录尝试次数限制'),
('session_expire_time', '7200', 'number', 'security', 'Session过期时间(秒)'),
('enable_ip_whitelist', '0', 'boolean', 'security', '是否启用IP白名单'),
('log_retention_days', '90', 'number', 'system', '日志保留天数'),
('cache_expire_time', '3600', 'number', 'system', '缓存过期时间(秒)')
ON DUPLICATE KEY UPDATE `config_value` = VALUES(`config_value`);

-- ========================================
-- 6. 创建基础表的必要索引 (仅创建基础索引，详细优化见 database_optimization.sql)
-- ========================================

-- ========================================
-- 7. 数据完整性约束
-- ========================================

-- 显示初始化完成信息
SELECT '数据库初始化完成！' as '初始化状态';
SELECT '默认管理员账号: admin' as '账号信息';
SELECT '默认管理员密码: admin123456' as '密码信息';
SELECT '请及时修改默认密码以确保系统安全！' as '安全提示';

SET FOREIGN_KEY_CHECKS = 1;