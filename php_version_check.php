<?php
/**
 * PHP版本和兼容性检查工具
 */

header("Content-type: text/html; charset=utf-8");

echo "<h1>PHP版本和兼容性检查</h1>";

// 基本PHP信息
echo "<h2>基本信息</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><td><strong>PHP版本</strong></td><td>" . phpversion() . "</td></tr>";
echo "<tr><td><strong>服务器软件</strong></td><td>" . (isset($_SERVER['SERVER_SOFTWARE']) ? $_SERVER['SERVER_SOFTWARE'] : '未知') . "</td></tr>";
echo "<tr><td><strong>操作系统</strong></td><td>" . php_uname() . "</td></tr>";
echo "<tr><td><strong>内存限制</strong></td><td>" . ini_get('memory_limit') . "</td></tr>";
echo "<tr><td><strong>最大执行时间</strong></td><td>" . ini_get('max_execution_time') . " 秒</td></tr>";
echo "<tr><td><strong>文件上传限制</strong></td><td>" . ini_get('upload_max_filesize') . "</td></tr>";
echo "<tr><td><strong>POST数据限制</strong></td><td>" . ini_get('post_max_size') . "</td></tr>";
echo "</table><br>";

// PHP版本兼容性检查
echo "<h2>语法兼容性检查</h2>";

$compatibility = array();

// 检查null合并操作符支持 (??)
if (version_compare(phpversion(), '7.0.0', '>=')) {
    $compatibility['null_coalescing'] = array('status' => 'supported', 'message' => '支持null合并操作符(??)');
} else {
    $compatibility['null_coalescing'] = array('status' => 'not_supported', 'message' => '不支持null合并操作符(??)，需要使用isset()替代');
}

// 检查数组简写语法支持 []
if (version_compare(phpversion(), '5.4.0', '>=')) {
    $compatibility['short_array'] = array('status' => 'supported', 'message' => '支持数组简写语法[]');
} else {
    $compatibility['short_array'] = array('status' => 'not_supported', 'message' => '不支持数组简写语法[]，必须使用array()');
}

// 检查匿名函数支持
if (version_compare(phpversion(), '5.3.0', '>=')) {
    $compatibility['anonymous_functions'] = array('status' => 'supported', 'message' => '支持匿名函数');
} else {
    $compatibility['anonymous_functions'] = array('status' => 'not_supported', 'message' => '不支持匿名函数');
}

// 检查命名空间支持
if (version_compare(phpversion(), '5.3.0', '>=')) {
    $compatibility['namespaces'] = array('status' => 'supported', 'message' => '支持命名空间');
} else {
    $compatibility['namespaces'] = array('status' => 'not_supported', 'message' => '不支持命名空间');
}

// 检查JSON扩展
if (extension_loaded('json')) {
    $compatibility['json'] = array('status' => 'supported', 'message' => 'JSON扩展已安装');
} else {
    $compatibility['json'] = array('status' => 'error', 'message' => 'JSON扩展未安装（必需）');
}

// 检查MySQLi扩展
if (extension_loaded('mysqli')) {
    $compatibility['mysqli'] = array('status' => 'supported', 'message' => 'MySQLi扩展已安装');
} else {
    $compatibility['mysqli'] = array('status' => 'error', 'message' => 'MySQLi扩展未安装（必需）');
}

// 检查PDO扩展
if (extension_loaded('pdo')) {
    $compatibility['pdo'] = array('status' => 'supported', 'message' => 'PDO扩展已安装');
} else {
    $compatibility['pdo'] = array('status' => 'warning', 'message' => 'PDO扩展未安装（可选）');
}

// 显示兼容性结果
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>检查项目</th><th>状态</th><th>说明</th></tr>";

foreach($compatibility as $key => $item) {
    $statusColor = '';
    if ($item['status'] == 'supported') $statusColor = 'color: green;';
    elseif ($item['status'] == 'warning') $statusColor = 'color: orange;';
    elseif ($item['status'] == 'error') $statusColor = 'color: red;';
    else $statusColor = 'color: red;';
    
    echo "<tr>";
    echo "<td>" . ucfirst(str_replace('_', ' ', $key)) . "</td>";
    echo "<td style='{$statusColor}'><strong>" . strtoupper($item['status']) . "</strong></td>";
    echo "<td>" . $item['message'] . "</td>";
    echo "</tr>";
}
echo "</table><br>";

// 安装建议
echo "<h2>安装建议</h2>";
$phpVersion = phpversion();

if (version_compare($phpVersion, '7.0.0', '>=')) {
    echo "<div style='color: green; border: 1px solid green; padding: 10px; margin: 10px 0;'>";
    echo "<strong>✓ PHP版本较新</strong><br>";
    echo "您的PHP版本({$phpVersion})支持现代PHP语法，建议使用原始的安装脚本。";
    echo "</div>";
} elseif (version_compare($phpVersion, '5.4.0', '>=')) {
    echo "<div style='color: orange; border: 1px solid orange; padding: 10px; margin: 10px 0;'>";
    echo "<strong>⚠ PHP版本较旧</strong><br>";
    echo "您的PHP版本({$phpVersion})需要使用兼容性修复后的安装脚本。<br>";
    echo "建议使用 <code>install_api.php</code> 进行安装。";
    echo "</div>";
} else {
    echo "<div style='color: red; border: 1px solid red; padding: 10px; margin: 10px 0;'>";
    echo "<strong>✗ PHP版本过旧</strong><br>";
    echo "您的PHP版本({$phpVersion})过于陈旧，可能无法正常运行此系统。<br>";
    echo "强烈建议升级到PHP 5.6或更高版本。";
    echo "</div>";
}

// 测试具体语法特性
echo "<h2>语法特性测试</h2>";

echo "<h3>1. 测试null合并操作符</h3>";
if (version_compare($phpVersion, '7.0.0', '>=')) {
    echo "<div style='color: green;'>✓ 支持 ?? 操作符</div>";
} else {
    echo "<div style='color: red;'>✗ 不支持 ?? 操作符，示例替代方案：</div>";
    echo "<pre>";
    echo "// 错误写法（PHP 7.0+）:\n";
    echo "\$value = \$_POST['key'] ?? 'default';\n\n";
    echo "// 正确写法（PHP 5.x兼容）:\n";
    echo "\$value = isset(\$_POST['key']) ? \$_POST['key'] : 'default';";
    echo "</pre>";
}

echo "<h3>2. 测试数组语法</h3>";
if (version_compare($phpVersion, '5.4.0', '>=')) {
    echo "<div style='color: green;'>✓ 支持 [] 数组语法</div>";
} else {
    echo "<div style='color: red;'>✗ 不支持 [] 数组语法，示例替代方案：</div>";
    echo "<pre>";
    echo "// 错误写法（PHP 5.4+）:\n";
    echo "\$array = ['key' => 'value'];\n\n";
    echo "// 正确写法（PHP 5.x兼容）:\n";
    echo "\$array = array('key' => 'value');";
    echo "</pre>";
}

// 功能测试
echo "<h2>功能测试</h2>";

echo "<h3>JSON编码测试</h3>";
$testData = array('status' => true, 'message' => '测试成功', 'data' => array('test' => 'value'));
$jsonResult = json_encode($testData);
if ($jsonResult !== false) {
    echo "<div style='color: green;'>✓ JSON编码功能正常</div>";
    echo "<pre>测试数据: " . $jsonResult . "</pre>";
} else {
    echo "<div style='color: red;'>✗ JSON编码功能异常</div>";
}

echo "<h3>MySQLi连接测试</h3>";
if (extension_loaded('mysqli')) {
    echo "<div style='color: green;'>✓ MySQLi扩展已加载，可以进行数据库连接</div>";
} else {
    echo "<div style='color: red;'>✗ MySQLi扩展未加载，无法连接MySQL数据库</div>";
}

// 推荐的安装方法
echo "<h2>推荐安装方法</h2>";

if (version_compare($phpVersion, '7.0.0', '>=')) {
    echo "<ol>";
    echo "<li><strong>优先选择：</strong> <a href='install.html' target='_blank'>install.html</a> - 现代化安装界面</li>";
    echo "<li><strong>备选方案：</strong> <a href='install_api.php' target='_blank'>install_api.php</a> - 纯API接口</li>";
    echo "<li><strong>调试工具：</strong> <a href='debug_install.php' target='_blank'>debug_install.php</a> - 问题诊断</li>";
    echo "</ol>";
} else {
    echo "<ol>";
    echo "<li><strong>推荐使用：</strong> <a href='install_api.php' target='_blank'>install_api.php</a> - 兼容低版本PHP</li>";
    echo "<li><strong>配合使用：</strong> <a href='install.html' target='_blank'>install.html</a> - 安装界面</li>";
    echo "<li><strong>问题诊断：</strong> <a href='debug_install.php' target='_blank'>debug_install.php</a> - 调试工具</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<p><small>检测时间: " . date('Y-m-d H:i:s') . "</small></p>";
echo "<p><small><a href='javascript:location.reload()'>刷新检测</a> | <a href='install.html'>开始安装</a></small></p>";
?>