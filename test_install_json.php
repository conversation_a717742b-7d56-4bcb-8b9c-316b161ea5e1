<?php
/**
 * 测试安装接口JSON响应
 */

header("Content-type: text/html; charset=utf-8");

echo "<h1>安装接口JSON响应测试</h1>";

// 测试数据
$testData = [
    'step' => 'install',
    'hostname' => 'localhost',
    'database' => 'test_db',
    'username' => 'root',
    'password' => '',
    'hostport' => '3306'
];

echo "<h2>测试数据</h2>";
echo "<pre>" . print_r($testData, true) . "</pre>";

echo "<h2>测试结果</h2>";

// 模拟POST请求
$postData = http_build_query($testData);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/x-www-form-urlencoded\r\n" . 
                   "Content-Length: " . strlen($postData) . "\r\n",
        'content' => $postData
    ]
]);

$url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/simple_install.php';

echo "<p><strong>请求URL:</strong> $url</p>";

try {
    $response = file_get_contents($url, false, $context);
    
    echo "<p><strong>原始响应:</strong></p>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 4px;'>" . htmlspecialchars($response) . "</pre>";
    
    // 尝试解析JSON
    $jsonData = json_decode($response, true);
    
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<p><strong>✅ JSON解析成功!</strong></p>";
        echo "<p><strong>解析后数据:</strong></p>";
        echo "<pre style='background: #d4edda; padding: 10px; border-radius: 4px;'>" . print_r($jsonData, true) . "</pre>";
    } else {
        echo "<p><strong>❌ JSON解析失败!</strong></p>";
        echo "<p><strong>错误信息:</strong> " . json_last_error_msg() . "</p>";
        
        // 检查响应是否包含HTML标签
        if (strpos($response, '<') !== false) {
            echo "<p><strong>⚠️ 响应包含HTML内容，可能是PHP错误或警告</strong></p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p><strong>❌ 请求失败:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>手动测试</h2>";
echo "<p>你也可以直接使用浏览器开发工具的Network标签页查看 setup.html 发送的请求和响应。</p>";
echo "<p><a href='setup.html' target='_blank'>打开 setup.html 进行手动测试</a></p>";
?>