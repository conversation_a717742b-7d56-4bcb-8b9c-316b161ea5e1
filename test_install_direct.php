<?php
/**
 * 直接测试安装过程
 */

header("Content-type: text/html; charset=utf-8");

echo "<h1>直接安装测试</h1>";

// 模拟POST数据
$_POST = [
    'step' => 'install',
    'hostname' => 'localhost',
    'database' => 'test_install_db',
    'username' => 'root',
    'password' => '',
    'hostport' => '3306'
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "<h2>测试参数</h2>";
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
foreach ($_POST as $key => $value) {
    echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>";
}
echo "</table>";

echo "<h2>执行安装过程</h2>";

try {
    // 捕获所有输出
    ob_start();
    
    // 包含安装API文件
    include 'install_api_pdo.php';
    
    // 获取输出
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "<h3>安装结果:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 4px;'>";
    echo htmlspecialchars($output);
    echo "</pre>";
    
    // 尝试解析JSON
    $result = json_decode($output, true);
    if ($result !== null) {
        echo "<h3>JSON解析成功:</h3>";
        echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 4px; color: #155724;'>";
        echo "<strong>状态:</strong> " . ($result['status'] ? '成功' : '失败') . "<br>";
        echo "<strong>消息:</strong> " . htmlspecialchars($result['message']);
        echo "</div>";
    } else {
        echo "<h3>JSON解析失败:</h3>";
        echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 4px; color: #721c24;'>";
        echo "无法解析为JSON格式";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 4px; color: #721c24;'>";
    echo "执行异常: " . $e->getMessage();
    echo "</div>";
}

echo "<hr>";
echo "<h2>数据库连接测试</h2>";

// 测试数据库连接
try {
    $link = @mysqli_connect('localhost', 'root', '', '', 3306);
    if ($link) {
        echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 4px; color: #155724;'>";
        echo "✅ 数据库连接成功<br>";
        echo "MySQL版本: " . mysqli_get_server_info($link);
        mysqli_close($link);
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 4px; color: #721c24;'>";
        echo "❌ 数据库连接失败: " . mysqli_connect_error();
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 4px; color: #721c24;'>";
    echo "❌ 数据库连接异常: " . $e->getMessage();
    echo "</div>";
}

echo "<p><a href='setup.html' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>返回安装页面</a></p>";
?>
