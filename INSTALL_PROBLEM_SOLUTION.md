# 安装问题解决方案

## 问题分析

您遇到的 "Unexpected token '<', ... is not valid JSON" 错误是由以下原因造成的：

### 根本原因
1. **PHP扩展缺失**: 原始安装脚本使用了 `mysqli_connect()` 函数，但您的PHP环境中没有启用 `mysqli` 扩展
2. **错误输出污染**: 当PHP函数不存在时，会输出HTML错误页面而不是JSON响应
3. **数据库连接配置**: 需要正确的MySQL用户名和密码

### 已修复的问题
✅ **创建了PDO版本的安装脚本** (`install_api_pdo.php`) - 使用PDO替代mysqli  
✅ **改进了错误处理** - 确保始终返回JSON格式响应  
✅ **添加了连接测试功能** - 可以在安装前测试数据库连接  
✅ **创建了修复版安装界面** (`setup_fixed.html`) - 更友好的用户界面  

## 解决方案

### 方案1: 使用修复版安装界面 (推荐)
1. 打开浏览器访问: `http://your-domain/setup_fixed.html`
2. 填写正确的数据库连接信息
3. 点击"测试连接"确认数据库可以正常连接
4. 点击"开始安装"完成系统安装

### 方案2: 手动配置数据库信息
如果您知道正确的数据库连接信息，可以直接修改 `config/database.php` 文件：

```php
<?php
return [
    'type' => 'mysql',
    'hostname' => 'localhost',        // 数据库主机
    'database' => 'your_database',    // 数据库名
    'username' => 'your_username',    // 用户名
    'password' => 'your_password',    // 密码
    'hostport' => '3306',            // 端口
    'prefix' => 'web_',
    'charset' => 'utf8mb4',
];
```

### 方案3: 启用mysqli扩展
如果您想使用原始的安装脚本，需要在PHP中启用mysqli扩展：

1. 找到 `php.ini` 文件
2. 取消注释或添加: `extension=mysqli`
3. 重启Web服务器

## 常见数据库连接问题

### 1. Access denied for user 'root'@'localhost'
**原因**: MySQL root用户需要密码  
**解决**: 在安装界面的密码字段中输入正确的root密码

### 2. Connection refused
**原因**: MySQL服务未启动或端口不正确  
**解决**: 
- 启动MySQL服务
- 确认MySQL运行在3306端口

### 3. Unknown database
**原因**: 指定的数据库不存在  
**解决**: 
- 使用安装脚本会自动创建数据库
- 或手动创建数据库

## 测试工具

我们提供了以下测试工具帮助诊断问题：

1. **`test_database_connection.php`** - 测试各种数据库连接配置
2. **`test_connection_api.php`** - API形式的连接测试
3. **`test_install_direct.php`** - 直接测试安装过程

## 安装后的默认账号

安装成功后，系统会创建默认管理员账号：
- **用户名**: admin
- **密码**: admin123456

⚠️ **重要**: 请在首次登录后立即修改默认密码！

## 文件说明

- `setup_fixed.html` - 修复版安装界面
- `install_api_pdo.php` - PDO版本的安装API
- `test_connection_api.php` - 数据库连接测试API
- `test_database_connection.php` - 数据库连接诊断工具

## 如果仍有问题

如果按照上述方案仍无法解决问题，请：

1. 检查PHP错误日志
2. 确认MySQL服务状态
3. 验证数据库用户权限
4. 检查防火墙设置

联系技术支持时，请提供：
- PHP版本信息
- MySQL版本信息
- 具体的错误信息
- 数据库连接配置
