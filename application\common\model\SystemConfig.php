<?php
namespace app\common\model;
use think\Db;
use think\Cache;

class SystemConfig extends Base{
	
    protected $name = 'system_config';
    
    /**
     * 获取配置值
     */
    public static function getValue($key, $default = ''){
        $cacheKey = "system_config_{$key}";
        $value = Cache::get($cacheKey);
        
        if ($value === false) {
            $config = Db::name('system_config')->where('config_key', $key)->find();
            $value = $config ? $config['config_value'] : $default;
            Cache::set($cacheKey, $value, 3600); // 缓存1小时
        }
        
        return $value;
    }
    
    /**
     * 设置配置值
     */
    public static function setValue($key, $value, $desc = ''){
        $data = [
            'config_key'   => $key,
            'config_value' => $value,
            'config_desc'  => $desc,
            'update_time'  => date('Y-m-d H:i:s'),
        ];
        
        $result = Db::name('system_config')->where('config_key', $key)->find();
        
        if ($result) {
            $res = Db::name('system_config')->where('config_key', $key)->update($data);
        } else {
            $res = Db::name('system_config')->insert($data);
        }
        
        if ($res) {
            Cache::rm("system_config_{$key}");
            return ['status'=>1,'msg'=>'配置更新成功！'];
        }
        
        return ['status'=>0,'msg'=>'配置更新失败！'];
    }
    
    /**
     * 获取分组配置
     */
    public function getGroupConfigs($group = 'system'){
        return $this->where('config_group', $group)->order('config_key')->select();
    }
    
    /**
     * 批量更新配置
     */
    public function batchUpdate($configs){
        $success = 0;
        $failed = 0;
        
        foreach ($configs as $key => $value) {
            $result = self::setValue($key, $value);
            if ($result['status'] == 1) {
                $success++;
            } else {
                $failed++;
            }
        }
        
        return [
            'status' => $failed > 0 ? 0 : 1,
            'msg' => "成功更新 {$success} 项配置" . ($failed > 0 ? "，失败 {$failed} 项" : ''),
            'success' => $success,
            'failed' => $failed
        ];
    }
}