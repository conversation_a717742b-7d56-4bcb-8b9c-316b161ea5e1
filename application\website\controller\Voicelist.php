<?php
namespace app\website\controller;
use app\website\controller\Base;
use think\Session;
use think\Request;

class Voicelist extends Base{
	
	public function Index(){
		$res = model("Voicelist")->GetAll($this->page);
		$page = $res->render();
		$this->assign('page', $page);
		$this->assign("list",$res);	
		return view();
	}
	
	public function Add(){
		if(Request::instance()->isPost()){
			$DATA = [
				'vol_title'   => trim(input("title")),
				'vol_content' => trim(input("content")),
			];
			$res = model("Voicelist")->Add($DATA);
			header('Content-Type: application/json');

			return _Json($res);
		}
		return view();
	}
	
	
	public function Edit(){
		$id = input('id');
		if(Request::instance()->isPost()){
			$DATA = [
				'vol_title'   => trim(input("title")),
				'vol_content' => trim(input("content")),
			];
			$res = model("Voicelist")->Edit($DATA,$id);
			header('Content-Type: application/json');

			return _Json($res);
		}
		$info = model("Voicelist")->GetOne($id);
		$this->assign("info",$info);
		return view();
	}
	

	public function Del(){
		if(Request::instance()->isPost()){
			$id = input('id');
			$res = model("Voicelist")->Del($id);
			header('Content-Type: application/json');

			return _Json($res);
		}
	}	
	
	
}