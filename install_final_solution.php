<?php
/**
 * 最终安装解决方案
 * 解决所有已知的JSON解析和数据库连接问题
 */

// === 严格的输出控制和错误处理 ===
while (ob_get_level()) {
    ob_end_clean();
}
ob_start();

// 完全关闭错误显示，防止污染JSON输出
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 设置错误处理器
set_error_handler(function($severity, $message, $file, $line) {
    // 记录错误到日志，但不输出
    error_log("Install Error: $message in $file on line $line");
    return true;
});

// 设置异常处理器
set_exception_handler(function($exception) {
    sendJsonResponse([
        'status' => false, 
        'message' => '系统异常: ' . $exception->getMessage()
    ]);
});

// === 定义常量 ===
define('INSTALL_LOCK', __DIR__ . '/install.lock');

// === 核心函数 ===
function sendJsonResponse($data) {
    // 清理所有输出缓冲
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    // 设置正确的响应头
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // 确保数据是数组格式
    if (!is_array($data)) {
        $data = ['status' => false, 'message' => '无效的响应数据'];
    }
    
    // 输出JSON并立即退出
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit(0);
}

function validateConfig($config) {
    $errors = [];
    
    if (empty($config['hostname'])) {
        $errors[] = '数据库主机不能为空';
    }
    
    if (empty($config['database'])) {
        $errors[] = '数据库名不能为空';
    }
    
    if (empty($config['username'])) {
        $errors[] = '数据库用户名不能为空';
    }
    
    $config['hostport'] = intval($config['hostport'] ?? 3306);
    if ($config['hostport'] <= 0 || $config['hostport'] > 65535) {
        $errors[] = '数据库端口无效';
    }
    
    return empty($errors) ? true : implode(', ', $errors);
}

function testDatabaseConnection($config) {
    try {
        $dsn = "mysql:host={$config['hostname']};port={$config['hostport']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 10,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
        
        return ['success' => true, 'pdo' => $pdo];
    } catch (PDOException $e) {
        $message = $e->getMessage();
        
        // 提供更友好的错误信息
        if (strpos($message, 'Access denied') !== false) {
            return ['success' => false, 'message' => '数据库访问被拒绝，请检查用户名和密码'];
        } elseif (strpos($message, 'Connection refused') !== false) {
            return ['success' => false, 'message' => '无法连接到数据库服务器，请检查主机和端口'];
        } elseif (strpos($message, 'Unknown MySQL server host') !== false) {
            return ['success' => false, 'message' => '无法解析数据库主机名'];
        } else {
            return ['success' => false, 'message' => '数据库连接失败: ' . $message];
        }
    }
}

function createDatabaseAndTables($pdo, $config) {
    try {
        // 创建数据库
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` DEFAULT CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$config['database']}`");
        
        // 创建核心表
        $tables = [
            "CREATE TABLE IF NOT EXISTS `web_users` (
              `u_id` int(11) NOT NULL AUTO_INCREMENT,
              `u_phone` varchar(20) NOT NULL,
              `u_password` varchar(64) NOT NULL,
              `u_nickname` varchar(50) DEFAULT '',
              `u_status` tinyint(1) DEFAULT 1,
              `u_supermanage` tinyint(1) DEFAULT 1,
              `u_count` int(11) DEFAULT 0,
              `u_regtime` datetime DEFAULT NULL,
              `u_this_time` datetime DEFAULT NULL,
              `u_this_ip` varchar(50) DEFAULT '',
              `u_last_time` datetime DEFAULT NULL,
              `u_last_ip` varchar(50) DEFAULT '',
              PRIMARY KEY (`u_id`),
              UNIQUE KEY `uk_phone` (`u_phone`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
            
            "CREATE TABLE IF NOT EXISTS `web_auth_rule` (
              `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
              `name` char(80) NOT NULL DEFAULT '',
              `title` char(20) NOT NULL DEFAULT '',
              `type` tinyint(1) NOT NULL DEFAULT 1,
              `status` tinyint(1) NOT NULL DEFAULT 1,
              `condition` char(100) NOT NULL DEFAULT '',
              `said` int(11) DEFAULT 0,
              `sort` int(11) DEFAULT 0,
              PRIMARY KEY (`id`),
              UNIQUE KEY `name` (`name`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
            
            "CREATE TABLE IF NOT EXISTS `web_auth_group` (
              `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
              `title` char(100) NOT NULL DEFAULT '',
              `status` tinyint(1) NOT NULL DEFAULT 1,
              `rules` char(80) NOT NULL DEFAULT '',
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
        ];
        
        $createdTables = 0;
        foreach ($tables as $sql) {
            $pdo->exec($sql);
            $createdTables++;
        }
        
        // 插入管理员账号
        $pdo->exec("INSERT IGNORE INTO `web_users` 
                   (`u_phone`, `u_password`, `u_nickname`, `u_status`, `u_supermanage`, `u_regtime`) 
                   VALUES ('admin', MD5('admin123456'), '系统管理员', 1, 2, NOW())");
        
        return ['success' => true, 'tables' => $createdTables];
        
    } catch (PDOException $e) {
        return ['success' => false, 'message' => '创建数据库或表失败: ' . $e->getMessage()];
    }
}

function generateConfigFiles($config) {
    try {
        // 确保配置目录存在
        if (!file_exists('config')) {
            mkdir('config', 0755, true);
        }
        if (!file_exists('config/extra')) {
            mkdir('config/extra', 0755, true);
        }
        
        // 生成数据库配置
        $dbConfig = "<?php\nreturn [\n";
        $dbConfig .= "    'type' => 'mysql',\n";
        $dbConfig .= "    'hostname' => '{$config['hostname']}',\n";
        $dbConfig .= "    'database' => '{$config['database']}',\n";
        $dbConfig .= "    'username' => '{$config['username']}',\n";
        $dbConfig .= "    'password' => '{$config['password']}',\n";
        $dbConfig .= "    'hostport' => '{$config['hostport']}',\n";
        $dbConfig .= "    'prefix' => 'web_',\n";
        $dbConfig .= "    'charset' => 'utf8mb4',\n";
        $dbConfig .= "    'debug' => true,\n";
        $dbConfig .= "];\n";
        
        file_put_contents('config/database.php', $dbConfig);
        
        // 生成IP白名单配置（默认关闭）
        $ipConfig = "<?php\nreturn [\n    'onoff' => false,\n    'ip' => 'localhost|127.0.0.1',\n];\n";
        file_put_contents('config/extra/ip.php', $ipConfig);
        
        // 创建必要目录
        $dirs = ['runtime', 'runtime/cache', 'runtime/temp', 'runtime/log', 'upload'];
        foreach ($dirs as $dir) {
            if (!file_exists($dir)) {
                mkdir($dir, 0755, true);
            }
        }
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// === 主逻辑 ===
try {
    // 只处理POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendJsonResponse(['status' => false, 'message' => '只接受POST请求']);
    }
    
    // 检查是否已安装
    if (file_exists(INSTALL_LOCK)) {
        sendJsonResponse(['status' => false, 'message' => '系统已安装，请删除install.lock文件后重试']);
    }
    
    // 获取并验证参数
    $step = $_POST['step'] ?? '';
    
    if ($step === 'test') {
        // 连接测试
        $config = [
            'hostname' => trim($_POST['hostname'] ?? ''),
            'database' => trim($_POST['database'] ?? ''),
            'username' => trim($_POST['username'] ?? ''),
            'password' => $_POST['password'] ?? '',
            'hostport' => intval($_POST['hostport'] ?? 3306)
        ];
        
        $validation = validateConfig($config);
        if ($validation !== true) {
            sendJsonResponse(['status' => false, 'message' => $validation]);
        }
        
        $result = testDatabaseConnection($config);
        if ($result['success']) {
            sendJsonResponse(['status' => true, 'message' => '数据库连接成功！']);
        } else {
            sendJsonResponse(['status' => false, 'message' => $result['message']]);
        }
        
    } elseif ($step === 'install') {
        // 执行安装
        $config = [
            'hostname' => trim($_POST['hostname'] ?? ''),
            'database' => trim($_POST['database'] ?? ''),
            'username' => trim($_POST['username'] ?? ''),
            'password' => $_POST['password'] ?? '',
            'hostport' => intval($_POST['hostport'] ?? 3306)
        ];
        
        $validation = validateConfig($config);
        if ($validation !== true) {
            sendJsonResponse(['status' => false, 'message' => $validation]);
        }
        
        // 测试数据库连接
        $connResult = testDatabaseConnection($config);
        if (!$connResult['success']) {
            sendJsonResponse(['status' => false, 'message' => $connResult['message']]);
        }
        
        // 创建数据库和表
        $createResult = createDatabaseAndTables($connResult['pdo'], $config);
        if (!$createResult['success']) {
            sendJsonResponse(['status' => false, 'message' => $createResult['message']]);
        }
        
        // 生成配置文件
        if (!generateConfigFiles($config)) {
            sendJsonResponse(['status' => false, 'message' => '生成配置文件失败']);
        }
        
        // 创建安装锁
        file_put_contents(INSTALL_LOCK, "安装时间: " . date('Y-m-d H:i:s') . "\n安装版本: Final Solution v1.0");
        
        sendJsonResponse([
            'status' => true, 
            'message' => '安装成功！已创建' . $createResult['tables'] . '个核心表，管理员账号：admin/admin123456'
        ]);
        
    } else {
        sendJsonResponse(['status' => false, 'message' => '无效的操作步骤']);
    }
    
} catch (Exception $e) {
    sendJsonResponse(['status' => false, 'message' => '系统异常: ' . $e->getMessage()]);
} catch (Error $e) {
    sendJsonResponse(['status' => false, 'message' => '系统错误: ' . $e->getMessage()]);
}
?>
