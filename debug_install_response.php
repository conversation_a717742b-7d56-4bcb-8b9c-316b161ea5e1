<?php
/**
 * 调试安装接口响应
 * 查看simple_install.php返回的原始内容
 */

header("Content-type: text/html; charset=utf-8");

echo "<h1>安装接口响应调试</h1>";

// 测试数据
$testData = [
    'step' => 'install',
    'hostname' => 'localhost',
    'database' => 'test_database_debug',
    'username' => 'root',
    'password' => '',
    'hostport' => '3306'
];

echo "<h2>测试参数</h2>";
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
foreach ($testData as $key => $value) {
    echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>";
}
echo "</table>";

echo "<h2>发送请求并获取原始响应</h2>";

// 构建POST数据
$postData = http_build_query($testData);

// 创建请求上下文
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            "Content-Type: application/x-www-form-urlencoded",
            "Content-Length: " . strlen($postData),
            "User-Agent: Mozilla/5.0 (debugging tool)"
        ],
        'content' => $postData,
        'timeout' => 30
    ]
]);

// 构建URL - 使用install_api.php
$url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/install_api.php';

echo "<p><strong>请求URL:</strong> <code>$url</code></p>";
echo "<p><strong>请求方法:</strong> POST</p>";
echo "<p><strong>POST数据:</strong> <code>" . htmlspecialchars($postData) . "</code></p>";

echo "<hr>";

try {
    echo "<h3>执行请求...</h3>";
    
    // 发送请求并获取响应
    $response = file_get_contents($url, false, $context);
    
    // 获取HTTP响应头
    $responseHeaders = $http_response_header ?? [];
    
    echo "<h3>HTTP响应头:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 4px;'>";
    foreach ($responseHeaders as $header) {
        echo htmlspecialchars($header) . "\n";
    }
    echo "</pre>";
    
    echo "<h3>响应内容长度:</h3>";
    echo "<p>" . strlen($response) . " bytes</p>";
    
    echo "<h3>原始响应内容:</h3>";
    echo "<textarea style='width: 100%; height: 200px; font-family: monospace; font-size: 12px;'>";
    echo htmlspecialchars($response);
    echo "</textarea>";
    
    echo "<h3>响应内容（十六进制）:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 11px; max-height: 200px; overflow-y: auto;'>";
    $hex = bin2hex($response);
    for ($i = 0; $i < strlen($hex); $i += 32) {
        $chunk = substr($hex, $i, 32);
        $ascii = '';
        for ($j = 0; $j < strlen($chunk); $j += 2) {
            $char = chr(hexdec(substr($chunk, $j, 2)));
            $ascii .= ctype_print($char) ? $char : '.';
        }
        echo sprintf("%08X: %-32s %s\n", $i/2, chunk_split($chunk, 2, ' '), $ascii);
    }
    echo "</pre>";
    
    echo "<h3>JSON解析测试:</h3>";
    $jsonData = json_decode($response, true);
    $jsonError = json_last_error();
    
    if ($jsonError === JSON_ERROR_NONE) {
        echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 4px; color: #155724;'>";
        echo "✅ JSON解析成功!<br>";
        echo "<strong>解析结果:</strong><br>";
        echo "<pre>" . print_r($jsonData, true) . "</pre>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 4px; color: #721c24;'>";
        echo "❌ JSON解析失败!<br>";
        echo "<strong>错误信息:</strong> " . json_last_error_msg() . "<br>";
        echo "<strong>错误代码:</strong> " . $jsonError . "<br>";
        echo "</div>";
        
        // 分析可能的问题
        echo "<h3>问题分析:</h3>";
        echo "<ul>";
        
        if (strpos($response, '<') !== false) {
            echo "<li>❌ 响应包含HTML标签，可能是PHP错误页面</li>";
        }
        
        if (strpos($response, 'Fatal error') !== false) {
            echo "<li>❌ 检测到PHP致命错误</li>";
        }
        
        if (strpos($response, 'Warning') !== false) {
            echo "<li>⚠️ 检测到PHP警告</li>";
        }
        
        if (strpos($response, 'Notice') !== false) {
            echo "<li>⚠️ 检测到PHP通知</li>";
        }
        
        if (empty(trim($response))) {
            echo "<li>❌ 响应为空</li>";
        }
        
        $firstChar = substr(trim($response), 0, 1);
        if ($firstChar !== '{' && $firstChar !== '[') {
            echo "<li>❌ 响应不以JSON格式开始（首字符: '$firstChar'）</li>";
        }
        
        echo "</ul>";
    }
    
    echo "<h3>Content-Type检查:</h3>";
    $contentType = 'unknown';
    foreach ($responseHeaders as $header) {
        if (stripos($header, 'Content-Type') === 0) {
            $contentType = trim(substr($header, strpos($header, ':') + 1));
            break;
        }
    }
    
    if (strpos($contentType, 'application/json') !== false) {
        echo "<div style='background: #d4edda; padding: 10px; color: #155724;'>✅ Content-Type正确: $contentType</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; color: #721c24;'>❌ Content-Type不正确: $contentType</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 4px; color: #721c24;'>";
    echo "❌ 请求失败: " . $e->getMessage();
    echo "</div>";
}

echo "<hr>";
echo "<h2>快速修复建议</h2>";
echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 4px;'>";
echo "如果发现问题，可以尝试：<br>";
echo "1. 检查PHP错误日志<br>";
echo "2. 临时启用PHP错误显示看具体错误信息<br>";
echo "3. 确保数据库连接信息正确<br>";
echo "4. 检查文件权限问题<br>";
echo "5. 查看 install_errors.log 文件";
echo "</div>";

echo "<p><a href='setup.html' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>返回安装页面</a></p>";
?>