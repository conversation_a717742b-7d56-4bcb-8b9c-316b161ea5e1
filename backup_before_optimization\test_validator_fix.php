<?php
/**
 * 验证器修复测试脚本
 */

header("Content-type: text/html; charset=utf-8");

echo "<h1>验证器修复测试</h1>";

// 测试数据
$testCases = [
    ['username' => 'admin', 'password' => 'admin123456', 'desc' => '管理员账号'],
    ['username' => '13800138000', 'password' => '123456', 'desc' => '手机号账号'],
    ['username' => '', 'password' => '123456', 'desc' => '空用户名'],
    ['username' => 'admin', 'password' => '123', 'desc' => '密码太短'],
    ['username' => 'test123', 'password' => '123456', 'desc' => '普通用户名'],
];

try {
    // 模拟ThinkPHP环境
    define('APP_PATH', __DIR__ . '/application/');
    require_once 'application/common.php';
    
    // 手动测试验证器
    foreach ($testCases as $index => $case) {
        echo "<h3>测试用例 " . ($index + 1) . ": {$case['desc']}</h3>";
        echo "用户名: {$case['username']}<br>";
        echo "密码: {$case['password']}<br>";
        
        try {
            // 加载验证器
            $validate = \think\Loader::validate('Users');
            
            // 测试登录场景验证
            if ($validate->scene('login')->check($case)) {
                echo "<span style='color: green;'>✓ 登录验证通过</span><br>";
            } else {
                echo "<span style='color: red;'>✗ 登录验证失败: " . $validate->getError() . "</span><br>";
            }
            
            // 测试添加用户场景验证
            if ($validate->scene('add')->check($case)) {
                echo "<span style='color: green;'>✓ 添加用户验证通过</span><br>";
            } else {
                echo "<span style='color: orange;'>! 添加用户验证失败: " . $validate->getError() . "</span><br>";
            }
            
        } catch (Exception $e) {
            echo "<span style='color: red;'>验证器加载失败: " . $e->getMessage() . "</span><br>";
        }
        
        echo "<hr>";
    }
    
    // 测试直接登录
    echo "<h3>实际登录测试</h3>";
    
    // 连接数据库
    $dbConfig = include 'config/database.php';
    $pdo = new PDO(
        "mysql:host={$dbConfig['hostname']};dbname={$dbConfig['database']};charset=utf8mb4",
        $dbConfig['username'],
        $dbConfig['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // 测试admin登录
    $adminData = ['username' => 'admin', 'password' => 'admin123456'];
    
    echo "测试admin登录...<br>";
    
    // 验证器检查
    $validate = \think\Loader::validate('Users');
    if ($validate->scene('login')->check($adminData)) {
        echo "✓ 验证器通过<br>";
        
        // 数据库查询
        $sql = "SELECT * FROM web_users WHERE u_phone = ? AND u_password = MD5(?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$adminData['username'], $adminData['password']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo "✓ 数据库查询成功<br>";
            echo "用户信息: {$user['u_nickname']} (状态: {$user['u_status']})<br>";
        } else {
            echo "✗ 数据库中未找到匹配用户<br>";
        }
    } else {
        echo "✗ 验证器检查失败: " . $validate->getError() . "<br>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>";
    echo "<strong>错误:</strong> " . $e->getMessage() . "<br>";
    echo "<strong>文件:</strong> " . $e->getFile() . ":" . $e->getLine() . "<br>";
    echo "</div>";
}

echo "<h3>测试登录页面</h3>";
echo "<button onclick=\"window.open('test_fixed_login.html', '_blank')\">打开登录测试页面</button><br><br>";
echo "<button onclick=\"window.open('/', '_blank')\">打开主登录页面</button>";

echo "<script>
console.log('验证器修复测试完成');
</script>";
?>