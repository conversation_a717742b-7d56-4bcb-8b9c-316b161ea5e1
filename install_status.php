<?php
/**
 * 安装状态检查和系统诊断
 */

header("Content-type: text/html; charset=utf-8");

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='utf-8'>
    <title>系统安装状态检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-ok { background: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status-error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status-warning { background: #fff3cd; color: #856404; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status-info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 4px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔍 系统安装状态检查</h1>";

// 检查安装锁文件
$installLock = __DIR__ . '/install.lock';
$isInstalled = file_exists($installLock);

echo "<h2>📋 安装状态</h2>";
if ($isInstalled) {
    $lockContent = file_get_contents($installLock);
    echo "<div class='status-ok'>✅ 系统已安装<br>$lockContent</div>";
} else {
    echo "<div class='status-warning'>⚠️ 系统尚未安装</div>";
}

// 检查PHP环境
echo "<h2>🐘 PHP环境检查</h2>";
echo "<table>";
echo "<tr><th>项目</th><th>值</th><th>状态</th></tr>";

$phpVersion = PHP_VERSION;
$phpOk = version_compare($phpVersion, '7.0.0', '>=');
echo "<tr><td>PHP版本</td><td>$phpVersion</td><td>" . ($phpOk ? "✅" : "❌") . "</td></tr>";

$extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "<tr><td>$ext 扩展</td><td>" . ($loaded ? "已加载" : "未加载") . "</td><td>" . ($loaded ? "✅" : "❌") . "</td></tr>";
}

$mysqliLoaded = extension_loaded('mysqli');
echo "<tr><td>mysqli 扩展</td><td>" . ($mysqliLoaded ? "已加载" : "未加载") . "</td><td>" . ($mysqliLoaded ? "✅" : "⚠️") . "</td></tr>";

echo "</table>";

// 检查目录权限
echo "<h2>📁 目录权限检查</h2>";
$dirs = ['config', 'runtime', 'upload', 'runtime/cache', 'runtime/temp', 'runtime/log'];
echo "<table>";
echo "<tr><th>目录</th><th>存在</th><th>可写</th><th>状态</th></tr>";

foreach ($dirs as $dir) {
    $exists = file_exists($dir);
    $writable = $exists ? is_writable($dir) : false;
    $status = $exists && $writable ? "✅" : ($exists ? "⚠️" : "❌");
    echo "<tr><td>$dir</td><td>" . ($exists ? "是" : "否") . "</td><td>" . ($writable ? "是" : "否") . "</td><td>$status</td></tr>";
}

echo "</table>";

// 检查配置文件
echo "<h2>⚙️ 配置文件检查</h2>";
$configFiles = [
    'config/database.php' => '数据库配置',
    'config/extra/ip.php' => 'IP白名单配置'
];

echo "<table>";
echo "<tr><th>配置文件</th><th>描述</th><th>状态</th></tr>";

foreach ($configFiles as $file => $desc) {
    $exists = file_exists($file);
    echo "<tr><td>$file</td><td>$desc</td><td>" . ($exists ? "✅ 存在" : "❌ 不存在") . "</td></tr>";
}

echo "</table>";

// 数据库连接测试
echo "<h2>🗄️ 数据库连接测试</h2>";

if (file_exists('config/database.php')) {
    $dbConfig = include 'config/database.php';
    
    echo "<div class='status-info'>";
    echo "<strong>当前数据库配置:</strong><br>";
    echo "主机: " . htmlspecialchars($dbConfig['hostname'] ?? 'N/A') . "<br>";
    echo "数据库: " . htmlspecialchars($dbConfig['database'] ?? 'N/A') . "<br>";
    echo "用户名: " . htmlspecialchars($dbConfig['username'] ?? 'N/A') . "<br>";
    echo "端口: " . htmlspecialchars($dbConfig['hostport'] ?? '3306') . "<br>";
    echo "</div>";
    
    try {
        $dsn = "mysql:host={$dbConfig['hostname']};port=" . ($dbConfig['hostport'] ?: 3306) . ";charset=utf8mb4";
        if (!empty($dbConfig['database'])) {
            $dsn .= ";dbname={$dbConfig['database']}";
        }
        
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5
        ]);
        
        $version = $pdo->query('SELECT VERSION()')->fetchColumn();
        echo "<div class='status-ok'>✅ 数据库连接成功<br>MySQL版本: $version</div>";
        
        // 检查核心表
        if (!empty($dbConfig['database'])) {
            $tables = ['web_users', 'web_auth_rule', 'web_auth_group'];
            $existingTables = [];
            
            foreach ($tables as $table) {
                try {
                    $stmt = $pdo->query("SELECT 1 FROM `$table` LIMIT 1");
                    $existingTables[] = $table;
                } catch (PDOException $e) {
                    // 表不存在
                }
            }
            
            if (count($existingTables) > 0) {
                echo "<div class='status-ok'>✅ 核心数据表已创建: " . implode(', ', $existingTables) . "</div>";
            } else {
                echo "<div class='status-warning'>⚠️ 核心数据表尚未创建</div>";
            }
        }
        
    } catch (PDOException $e) {
        echo "<div class='status-error'>❌ 数据库连接失败: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
} else {
    echo "<div class='status-warning'>⚠️ 数据库配置文件不存在</div>";
}

// 操作建议
echo "<h2>🛠️ 操作建议</h2>";

if (!$isInstalled) {
    echo "<div class='status-info'>";
    echo "<strong>系统尚未安装，请选择以下方式之一进行安装：</strong><br><br>";
    echo "<a href='setup_fixed.html' class='btn btn-success'>使用修复版安装界面 (推荐)</a>";
    echo "<a href='setup.html' class='btn'>使用原版安装界面</a>";
    echo "<a href='test_database_connection.php' class='btn btn-warning'>数据库连接诊断</a>";
    echo "</div>";
} else {
    echo "<div class='status-ok'>";
    echo "<strong>系统已安装完成！</strong><br><br>";
    echo "<a href='index.php' class='btn btn-success'>进入系统</a>";
    echo "<a href='test_database_connection.php' class='btn'>数据库状态检查</a>";
    
    if (file_exists($installLock)) {
        echo "<br><br><div class='status-warning'>";
        echo "⚠️ <strong>安全提示:</strong> 建议删除安装相关文件以确保系统安全<br>";
        echo "可删除的文件: setup.html, setup_fixed.html, install_api.php, install_api_pdo.php, test_*.php";
        echo "</div>";
    }
    echo "</div>";
}

// 故障排除
echo "<h2>🔧 故障排除</h2>";
echo "<div class='status-info'>";
echo "<strong>如果遇到问题，请检查：</strong><br>";
echo "1. MySQL服务是否正常运行<br>";
echo "2. 数据库用户名和密码是否正确<br>";
echo "3. PHP是否启用了PDO和PDO_MySQL扩展<br>";
echo "4. 相关目录是否具有写入权限<br>";
echo "5. 查看详细的解决方案: <a href='INSTALL_PROBLEM_SOLUTION.md' target='_blank'>安装问题解决方案</a>";
echo "</div>";

echo "<hr>";
echo "<p><small>检查时间: " . date('Y-m-d H:i:s') . " | PHP版本: " . PHP_VERSION . "</small></p>";

echo "    </div>
</body>
</html>";
?>
