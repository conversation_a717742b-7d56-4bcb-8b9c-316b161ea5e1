<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>登录功能测试 - 修复版</title>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .login-form { max-width: 400px; margin: 0 auto; padding: 20px; border: 1px solid #ccc; border-radius: 5px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; }
        input { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 3px; }
        button { width: 100%; padding: 10px; background: #007cff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 10px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="login-form">
        <h2>登录功能测试</h2>
        
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="username" value="admin">
        </div>
        
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="password" value="admin123456">
        </div>
        
        <div class="form-group">
            <button onclick="testDirectLogin()">直接登录测试</button>
        </div>
        
        <div class="form-group">
            <button onclick="testThinkPHPLogin()">ThinkPHP框架登录</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
    function testDirectLogin() {
        var username = $('#username').val();
        var password = $('#password').val();
        
        $.ajax({
            type: "POST",
            url: "/direct_login_test.php",
            dataType: "json",
            data: {
                username: username,
                password: password
            },
            beforeSend: function() {
                $('#result').html('正在测试直接登录...');
            },
            success: function(res) {
                console.log('直接登录响应:', res);
                if(res.status == 1) {
                    $('#result').html('<div class="result success">直接登录成功: ' + res.msg + '</div>');
                } else {
                    $('#result').html('<div class="result error">直接登录失败: ' + res.msg + '</div>');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log('直接登录错误:', textStatus, errorThrown);
                $('#result').html('<div class="result error">直接登录请求失败: ' + textStatus + '<br>响应: ' + jqXHR.responseText + '</div>');
            }
        });
    }
    
    function testThinkPHPLogin() {
        var username = $('#username').val();
        var password = $('#password').val();
        
        $.ajax({
            type: "POST",
            url: "/website/index/Login",
            dataType: "json",
            data: {
                username: username,
                password: password
            },
            beforeSend: function() {
                $('#result').html('正在测试ThinkPHP登录...');
            },
            success: function(res) {
                console.log('ThinkPHP登录响应:', res);
                if(res.status == 1) {
                    $('#result').html('<div class="result success">ThinkPHP登录成功: ' + res.msg + '</div>');
                } else {
                    $('#result').html('<div class="result error">ThinkPHP登录失败: ' + res.msg + '</div>');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log('ThinkPHP登录错误:', textStatus, errorThrown, jqXHR.responseText);
                $('#result').html('<div class="result error">ThinkPHP登录请求失败: ' + jqXHR.status + '<br>错误: ' + textStatus + '<br>响应: <pre>' + jqXHR.responseText + '</pre></div>');
            }
        });
    }
    </script>
</body>
</html>