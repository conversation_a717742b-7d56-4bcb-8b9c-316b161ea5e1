<?php
/**
 * 简化登录入口 - 替代ThinkPHP路由
 */

header("Content-type: application/json; charset=utf-8");
error_reporting(E_ALL);
ini_set('display_errors', 0); // 生产环境关闭错误显示

// 只处理POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 0, 'msg' => '只支持POST请求']);
    exit;
}

try {
    // 包含ThinkPHP环境（如果需要）
    if (file_exists('thinkphp/start.php')) {
        // 定义常量
        define('APP_PATH', __DIR__ . '/application/');
        define('CONF_PATH', __DIR__ . '/config/');
        
        // 设置基本环境
        require_once 'application/common.php';
    }
    
    // 获取登录数据
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    
    if (empty($username) || empty($password)) {
        echo json_encode(['status' => 1001, 'msg' => '用户名和密码不能为空']);
        exit;
    }
    
    // 连接数据库
    $dbConfig = include 'config/database.php';
    $pdo = new PDO(
        "mysql:host={$dbConfig['hostname']};dbname={$dbConfig['database']};charset=utf8mb4",
        $dbConfig['username'],
        $dbConfig['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // 简单验证
    if (strlen($username) < 1 || strlen($password) < 6) {
        echo json_encode(['status' => 1001, 'msg' => '用户名或密码格式不正确']);
        exit;
    }
    
    // 查询用户
    $sql = "SELECT * FROM web_users WHERE u_phone = ? AND u_password = MD5(?)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$username, $password]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode(['status' => 1002, 'msg' => '此帐号不存在!']);
        exit;
    }
    
    if ($user['u_status'] == 2) {
        echo json_encode(['status' => 1003, 'msg' => '此帐号已被禁用!']);
        exit;
    }
    
    // 获取客户端IP
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $clientIP = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $clientIP = $_SERVER['HTTP_CLIENT_IP'];
    }
    
    // 启动session
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // 设置登录session
    $_SESSION['uid'] = $user['u_id'];
    $_SESSION['uphone'] = $user['u_phone'];
    $_SESSION['unickname'] = $user['u_nickname'];
    
    // 更新用户登录信息
    $updateSql = "UPDATE web_users SET 
                    u_count = u_count + 1,
                    u_this_time = NOW(),
                    u_this_ip = ?,
                    u_last_time = u_this_time,
                    u_last_ip = u_this_ip
                  WHERE u_id = ?";
    $stmt = $pdo->prepare($updateSql);
    $stmt->execute([$clientIP, $user['u_id']]);
    
    // 记录登录日志
    $logSql = "INSERT INTO web_users_logs (u_id, ul_type, ul_addtime, ul_ip) VALUES (?, 1, NOW(), ?)";
    $stmt = $pdo->prepare($logSql);
    $stmt->execute([$user['u_id'], $clientIP]);
    
    // 返回成功结果
    echo json_encode([
        'status' => 1,
        'msg' => '登录成功',
        'userinfo' => [
            'u_id' => $user['u_id'],
            'u_phone' => $user['u_phone'],
            'u_nickname' => $user['u_nickname']
        ]
    ]);
    
} catch (PDOException $e) {
    error_log("数据库错误: " . $e->getMessage());
    echo json_encode(['status' => 1004, 'msg' => '数据库连接失败']);
} catch (Exception $e) {
    error_log("系统错误: " . $e->getMessage());
    echo json_encode(['status' => 1005, 'msg' => '系统内部错误']);
}
?>