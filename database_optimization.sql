-- ========================================
-- 数据库性能优化脚本
-- 添加缺失的索引和优化查询性能
-- ========================================

-- 设置字符集
SET NAMES utf8mb4;

-- 显示优化信息
SELECT '开始数据库性能优化...' as '优化状态';

-- ========================================
-- 1. 用户相关表索引优化
-- ========================================

-- 用户表索引优化
ALTER TABLE `web_users` ADD INDEX `idx_phone_status` (`u_phone`, `u_status`);
ALTER TABLE `web_users` ADD INDEX `idx_regtime` (`u_regtime`);
ALTER TABLE `web_users` ADD INDEX `idx_last_time` (`u_last_time`);

-- 用户登录日志表索引优化
ALTER TABLE `web_userslogs` ADD INDEX `idx_uid_type_time` (`u_id`, `ul_type`, `ul_addtime`);
ALTER TABLE `web_userslogs` ADD INDEX `idx_ip_time` (`ul_ip`, `ul_addtime`);

-- 权限相关表索引优化
ALTER TABLE `web_auth_rule` ADD INDEX `idx_status_said_sort` (`status`, `said`, `sort`);
ALTER TABLE `web_auth_group` ADD INDEX `idx_status_title` (`status`, `title`);

-- ========================================
-- 2. 分站相关表索引优化
-- ========================================

-- 分站表额外索引
ALTER TABLE `web_substation` ADD INDEX `idx_name_status` (`su_name`, `su_status`);
ALTER TABLE `web_substation` ADD INDEX `idx_addtime_status` (`su_addtime`, `su_status`);
ALTER TABLE `web_substation` ADD INDEX `idx_endtime_status` (`su_endtime`, `su_status`);
ALTER TABLE `web_substation` ADD INDEX `idx_fz_money` (`su_fz_money`);
ALTER TABLE `web_substation` ADD INDEX `idx_dk_balance` (`su_dk`);

-- 分站群组表索引优化
ALTER TABLE `web_substation_group` ADD INDEX `idx_title_scale` (`su_g_title`, `su_g_scale`);
ALTER TABLE `web_substation_group` ADD INDEX `idx_addtime` (`su_g_addtime`);

-- 分站支付配置表索引优化
ALTER TABLE `web_substation_paylist` ADD INDEX `idx_su_pl_status` (`su_pl_status`);
ALTER TABLE `web_substation_paylist` ADD INDEX `idx_addtime` (`su_pl_addtime`);

-- ========================================
-- 3. 分销相关表索引优化
-- ========================================

-- 分销人员表额外索引
ALTER TABLE `web_distribution` ADD INDEX `idx_name_status` (`du_name`, `du_status`);
ALTER TABLE `web_distribution` ADD INDEX `idx_money_status` (`du_money`, `du_status`);
ALTER TABLE `web_distribution` ADD INDEX `idx_addtime_status` (`du_addtime`, `du_status`);

-- 分销群组表索引优化
ALTER TABLE `web_distribution_group` ADD INDEX `idx_su_title` (`su_id`, `dg_title`);
ALTER TABLE `web_distribution_group` ADD INDEX `idx_scale_addtime` (`dg_scale`, `dg_addtime`);

-- 分销提现表额外索引
ALTER TABLE `web_distribution_tixian` ADD INDEX `idx_money_status` (`dt_money`, `dt_status`);
ALTER TABLE `web_distribution_tixian` ADD INDEX `idx_dealtime` (`dt_dealtime`);

-- ========================================
-- 4. 业务核心表索引优化
-- ========================================

-- 微信群组表额外索引
ALTER TABLE `web_wxgroup` ADD INDEX `idx_title_status` (`wxg_title`, `wxg_status`);
ALTER TABLE `web_wxgroup` ADD INDEX `idx_addtime_status` (`wxg_addtime`, `wxg_status`);

-- 微信群组模板表索引优化
ALTER TABLE `web_wxgroup_template` ADD INDEX `idx_title_status` (`wt_title`, `wt_status`);
ALTER TABLE `web_wxgroup_template` ADD INDEX `idx_addtime` (`wt_addtime`);

-- 账单表额外索引（最重要的性能优化）
ALTER TABLE `web_bill` ADD INDEX `idx_orderid_status` (`bl_orderid`, `bl_status`);
ALTER TABLE `web_bill` ADD INDEX `idx_money_status` (`bl_money`, `bl_status`);
ALTER TABLE `web_bill` ADD INDEX `idx_addtime_status` (`bl_addtime`, `bl_status`);
ALTER TABLE `web_bill` ADD INDEX `idx_paytime_status` (`bl_paytime`, `bl_status`);
ALTER TABLE `web_bill` ADD INDEX `idx_su_du_status` (`su_id`, `du_id`, `bl_status`);

-- ========================================
-- 5. 支付相关表索引优化
-- ========================================

-- 支付方式表索引优化
ALTER TABLE `web_paylist` ADD INDEX `idx_title_status` (`pl_title`, `pl_status`);
ALTER TABLE `web_paylist` ADD INDEX `idx_addtime` (`pl_addtime`);

-- ========================================
-- 6. 财务相关表索引优化
-- ========================================

-- 对账表索引优化
ALTER TABLE `web_duizhang` ADD INDEX `idx_type_status` (`dz_type`, `dz_status`);
ALTER TABLE `web_duizhang` ADD INDEX `idx_money_status` (`dz_money`, `dz_status`);

-- 抽佣表索引优化
ALTER TABLE `web_chouyong` ADD INDEX `idx_scale_status` (`cy_scale`, `cy_status`);
ALTER TABLE `web_chouyong` ADD INDEX `idx_money_addtime` (`cy_money`, `cy_addtime`);

-- 点卡表索引优化
ALTER TABLE `web_dianka` ADD INDEX `idx_money_status` (`dk_money`, `dk_status`);
ALTER TABLE `web_dianka` ADD INDEX `idx_usetime` (`dk_usetime`);

-- 点卡日志表索引优化
ALTER TABLE `web_dianka_log` ADD INDEX `idx_type_money` (`dkl_type`, `dkl_money`);

-- 分站提现表索引优化
ALTER TABLE `web_substation_tixian` ADD INDEX `idx_money_status` (`su_money`, `su_status`);
ALTER TABLE `web_substation_tixian` ADD INDEX `idx_dealtime` (`su_dealtime`);

-- ========================================
-- 7. 系统表索引优化
-- ========================================

-- 系统设置表索引优化
ALTER TABLE `web_setup` ADD INDEX `idx_title_addtime` (`s_title`, `s_addtime`);

-- 导航菜单表索引优化
ALTER TABLE `web_navigat_show` ADD INDEX `idx_parent_sort_status` (`sns_id`, `ns_sort`, `ns_status`);
ALTER TABLE `web_navigat_show` ADD INDEX `idx_controller_method` (`ns_controller`, `ns_method`);

-- ========================================
-- 8. 扩展表索引优化
-- ========================================

-- 每日统计汇总表索引优化
ALTER TABLE `web_daily_summary` ADD INDEX `idx_date_user_type` (`ds_date`, `ds_user_type`);
ALTER TABLE `web_daily_summary` ADD INDEX `idx_user_id_date` (`ds_user_id`, `ds_date`);
ALTER TABLE `web_daily_summary` ADD INDEX `idx_update_time` (`ds_update_time`);

-- 实时缓存表索引优化
ALTER TABLE `web_realtime_cache` ADD INDEX `idx_key_expire` (`rc_key`, `rc_expire_time`);
ALTER TABLE `web_realtime_cache` ADD INDEX `idx_update_time` (`rc_update_time`);

-- 用户在线状态表索引优化
ALTER TABLE `web_user_online` ADD INDEX `idx_user_type_id_activity` (`uo_user_type`, `uo_user_id`, `uo_last_activity`);
ALTER TABLE `web_user_online` ADD INDEX `idx_session_activity` (`uo_session_id`, `uo_last_activity`);

-- 性能监控表索引优化
ALTER TABLE `web_performance_monitor` ADD INDEX `idx_date_type` (`pm_date`, `pm_query_type`);
ALTER TABLE `web_performance_monitor` ADD INDEX `idx_execution_time` (`pm_execution_time`);

-- 用户偏好设置表索引优化
ALTER TABLE `web_user_preferences` ADD INDEX `idx_user_type_id` (`up_user_type`, `up_user_id`);
ALTER TABLE `web_user_preferences` ADD INDEX `idx_update_time` (`up_update_time`);

-- 通知表索引优化
ALTER TABLE `web_notifications` ADD INDEX `idx_user_type_id_status` (`n_user_type`, `n_user_id`, `n_status`);
ALTER TABLE `web_notifications` ADD INDEX `idx_type_addtime` (`n_type`, `n_addtime`);

-- 操作日志表索引优化
ALTER TABLE `web_operation_logs` ADD INDEX `idx_user_type_id_time` (`ol_user_type`, `ol_user_id`, `ol_addtime`);
ALTER TABLE `web_operation_logs` ADD INDEX `idx_action_time` (`ol_action`, `ol_addtime`);
ALTER TABLE `web_operation_logs` ADD INDEX `idx_module_controller` (`ol_module`, `ol_controller`);

-- 用户行为表索引优化
ALTER TABLE `web_user_behavior` ADD INDEX `idx_user_type_id_action` (`ub_user_type`, `ub_user_id`, `ub_action`);
ALTER TABLE `web_user_behavior` ADD INDEX `idx_action_time` (`ub_action`, `ub_addtime`);

-- ========================================
-- 9. 复合索引优化（针对常用查询）
-- ========================================

-- 账单相关复合索引
ALTER TABLE `web_bill` ADD INDEX `idx_su_status_time_money` (`su_id`, `bl_status`, `bl_addtime`, `bl_money`);
ALTER TABLE `web_bill` ADD INDEX `idx_du_status_time_money` (`du_id`, `bl_status`, `bl_addtime`, `bl_scalemoney`);

-- 分销相关复合索引
ALTER TABLE `web_distribution` ADD INDEX `idx_su_status_money_time` (`su_id`, `du_status`, `du_money`, `du_addtime`);

-- 分站相关复合索引
ALTER TABLE `web_substation` ADD INDEX `idx_parent_status_time` (`su_s_id`, `su_status`, `su_addtime`);

-- ========================================
-- 10. 查询优化建议
-- ========================================

-- 显示优化完成信息
SELECT '数据库索引优化完成！' as '优化状态';

-- 统计新增索引数量
SELECT 
    COUNT(*) as new_index_count,
    '新增索引数量' as description
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name LIKE 'web_%' 
AND index_name LIKE 'idx_%'
AND index_name NOT IN (
    'idx_status_su_time', 'idx_status_du_time', 'idx_status_wxg_time',
    'idx_orderid_status', 'idx_paytime', 'idx_status_group', 'idx_parent_status',
    'idx_endtime_status', 'idx_domain_status', 'idx_su_status_time',
    'idx_group_status', 'idx_name_status', 'idx_du_status_time',
    'idx_su_status_time', 'idx_money_status', 'idx_status_time',
    'idx_du_status', 'idx_su_status', 'idx_su_time_type'
);

-- 显示优化建议
SELECT '建议定期执行 ANALYZE TABLE 和 OPTIMIZE TABLE 来维护索引性能' as '维护建议';
SELECT '建议监控慢查询日志，持续优化查询性能' as '监控建议';
SELECT '建议根据实际业务查询模式调整索引策略' as '调优建议';
