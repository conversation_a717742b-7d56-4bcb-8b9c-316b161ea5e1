<?php
/**
 * 纯JSON API安装脚本
 * 专门用于处理安装请求，避免任何HTML输出污染
 */

// 关闭所有错误显示，防止污染JSON输出
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 开始输出缓冲
ob_start();

// 设置内容类型为JSON
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, no-store, must-revalidate');

// 只允许POST请求
if($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_clean();
    echo json_encode(array('status' => false, 'message' => '只允许POST请求'), JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查是否已安装
$installLock = __DIR__ . '/install.lock';
if(file_exists($installLock)) {
    ob_clean();
    echo json_encode(array('status' => false, 'message' => '系统已经安装，如需重新安装请删除install.lock文件'), JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 获取配置参数
    $config = array(
        'hostname' => trim(isset($_POST['hostname']) ? $_POST['hostname'] : 'localhost'),
        'database' => trim(isset($_POST['database']) ? $_POST['database'] : ''),
        'username' => trim(isset($_POST['username']) ? $_POST['username'] : ''),
        'password' => isset($_POST['password']) ? $_POST['password'] : '',
        'hostport' => intval(isset($_POST['hostport']) ? $_POST['hostport'] : 3306)
    );
    
    // 验证必需参数
    if(empty($config['database'])) {
        throw new Exception('数据库名称不能为空');
    }
    if(empty($config['username'])) {
        throw new Exception('数据库用户名不能为空');
    }
    
    // 尝试数据库连接
    $link = @mysqli_connect(
        $config['hostname'],
        $config['username'],
        $config['password'],
        '',
        $config['hostport']
    );
    
    if(!$link) {
        throw new Exception('数据库连接失败: ' . mysqli_connect_error());
    }
    
    // 设置字符编码
    if(!mysqli_set_charset($link, 'utf8mb4')) {
        mysqli_close($link);
        throw new Exception('设置数据库字符编码失败');
    }
    
    // 创建数据库
    $createDbSql = "CREATE DATABASE IF NOT EXISTS `{$config['database']}` DEFAULT CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci";
    if(!@mysqli_query($link, $createDbSql)) {
        mysqli_close($link);
        throw new Exception('创建数据库失败: ' . mysqli_error($link));
    }
    
    // 选择数据库
    if(!@mysqli_select_db($link, $config['database'])) {
        mysqli_close($link);
        throw new Exception('选择数据库失败: ' . mysqli_error($link));
    }
    
    // 读取并执行SQL文件
    $sqlFile = __DIR__ . '/database_complete_init.sql';
    if(!file_exists($sqlFile)) {
        mysqli_close($link);
        throw new Exception('数据库初始化文件不存在');
    }
    
    $sql = file_get_contents($sqlFile);
    if($sql === false) {
        mysqli_close($link);
        throw new Exception('读取数据库初始化文件失败');
    }
    
    // 分割SQL语句
    $statements = explode(';', $sql);
    $createdTables = 0;
    $insertedRows = 0;
    
    foreach($statements as $statement) {
        $statement = trim($statement);
        
        // 跳过空语句和注释
        if(empty($statement) || 
           strpos($statement, '--') === 0 || 
           strpos($statement, 'SET ') === 0 ||
           strpos($statement, 'SET NAMES') === 0 ||
           strpos($statement, 'SET FOREIGN_KEY_CHECKS') === 0) {
            continue;
        }
        
        if(!@mysqli_query($link, $statement)) {
            $error = mysqli_error($link);
            // 忽略表已存在的错误
            if(!preg_match('/(already exists|duplicate|table.*exists)/i', $error)) {
                error_log("SQL Error: " . $error . " Statement: " . substr($statement, 0, 100));
            }
        } else {
            if(stripos($statement, 'CREATE TABLE') !== false) {
                $createdTables++;
            } elseif(stripos($statement, 'INSERT') !== false) {
                $insertedRows++;
            }
        }
    }
    
    // 插入管理员账户（如果不存在）
    $adminCheckSql = "SELECT COUNT(*) FROM web_users WHERE u_phone = 'admin'";
    $result = @mysqli_query($link, $adminCheckSql);
    
    if($result && mysqli_fetch_array($result)[0] == 0) {
        $adminSql = "INSERT INTO web_users (u_phone, u_password, u_nickname, u_status, u_supermanage, u_regtime) VALUES ('admin', MD5('admin123456'), '系统管理员', 1, 2, NOW())";
        if(@mysqli_query($link, $adminSql)) {
            $insertedRows++;
        }
    }
    
    mysqli_close($link);
    
    // 生成数据库配置文件
    if(!generateConfigFiles($config)) {
        throw new Exception('生成配置文件失败');
    }
    
    // 创建安装锁文件
    $remoteAddr = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
    if(file_put_contents($installLock, "安装时间: " . date('Y-m-d H:i:s') . "\n安装IP: " . $remoteAddr) === false) {
        throw new Exception('创建安装锁失败');
    }
    
    // 清理输出缓冲区并返回成功响应
    ob_clean();
    echo json_encode(array(
        'status' => true,
        'message' => '数据库初始化成功',
        'data' => array(
            'tables_created' => $createdTables,
            'data_inserted' => $insertedRows,
            'admin_account' => 'admin',
            'admin_password' => 'admin123456'
        )
    ), JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // 记录错误到日志
    error_log("Installation Error: " . $e->getMessage());
    
    // 清理输出缓冲区并返回错误响应
    ob_clean();
    echo json_encode(array(
        'status' => false,
        'message' => $e->getMessage(),
        'code' => $e->getCode()
    ), JSON_UNESCAPED_UNICODE);
}

exit;

/**
 * 生成配置文件
 */
function generateConfigFiles($config) {
    try {
        // 确保config目录存在
        $configDir = __DIR__ . '/config';
        if(!file_exists($configDir)) {
            if(!mkdir($configDir, 0755, true)) {
                return false;
            }
        }
        
        // 生成数据库配置
        $dbConfigContent = "<?php\n// 数据库配置文件\nreturn array(\n";
        $dbConfigContent .= "    'type'            => 'mysql',\n";
        $dbConfigContent .= "    'hostname'        => '{$config['hostname']}',\n";
        $dbConfigContent .= "    'database'        => '{$config['database']}',\n";
        $dbConfigContent .= "    'username'        => '{$config['username']}',\n";
        $dbConfigContent .= "    'password'        => '" . addslashes($config['password']) . "',\n";
        $dbConfigContent .= "    'hostport'        => '{$config['hostport']}',\n";
        $dbConfigContent .= "    'dsn'             => '',\n";
        $dbConfigContent .= "    'params'          => [],\n";
        $dbConfigContent .= "    'charset'         => 'utf8mb4',\n";
        $dbConfigContent .= "    'prefix'          => 'web_',\n";
        $dbConfigContent .= "    'debug'           => false,\n";
        $dbConfigContent .= "    'deploy'          => 0,\n";
        $dbConfigContent .= "    'rw_separate'     => false,\n";
        $dbConfigContent .= "    'master_num'      => 1,\n";
        $dbConfigContent .= "    'slave_no'        => '',\n";
        $dbConfigContent .= "    'read_master'     => false,\n";
        $dbConfigContent .= "    'fields_strict'   => true,\n";
        $dbConfigContent .= "    'resultset_type'  => 'array',\n";
        $dbConfigContent .= "    'auto_timestamp'  => false,\n";
        $dbConfigContent .= "    'datetime_format' => 'Y-m-d H:i:s',\n";
        $dbConfigContent .= "    'sql_explain'     => false,\n";
        $dbConfigContent .= ");\n";
        
        if(file_put_contents($configDir . '/database.php', $dbConfigContent) === false) {
            return false;
        }
        
        // 生成IP白名单配置（默认关闭）
        $extraDir = $configDir . '/extra';
        if(!file_exists($extraDir)) {
            if(!mkdir($extraDir, 0755, true)) {
                return false;
            }
        }
        
        $currentHost = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'localhost';
        $ipConfigContent = "<?php\n// IP白名单配置\nreturn array(\n";
        $ipConfigContent .= "    'onoff' => false, // 默认关闭IP白名单\n";
        $ipConfigContent .= "    'ip' => \"{$currentHost}|localhost|127.0.0.1\",\n";
        $ipConfigContent .= ");\n";
        
        if(file_put_contents($extraDir . '/ip.php', $ipConfigContent) === false) {
            return false;
        }
        
        // 创建必要目录
        $dirs = array('runtime', 'runtime/cache', 'runtime/temp', 'runtime/log', 'upload');
        foreach($dirs as $dir) {
            $fullPath = __DIR__ . '/' . $dir;
            if(!file_exists($fullPath)) {
                if(!mkdir($fullPath, 0755, true)) {
                    error_log("Failed to create directory: " . $fullPath);
                }
            }
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Config generation error: " . $e->getMessage());
        return false;
    }
}
?>