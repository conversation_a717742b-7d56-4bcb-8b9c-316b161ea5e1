<?php
namespace app\common\model;
use think\Db;
use think\Request;

class Voicelist extends Base{
	
    protected $name = 'voicelist';
    
    public function GetAll($page){
        return $this->where("1=1")->order("vol_id desc")->paginate($page);
    }
    
    public function GetOne($id){
        if(empty($id)) return false;
        return $this->where("vol_id = {$id}")->limit(1)->find();
    }
    
    public function Add($data){
        $validate = \think\Loader::validate('Voicelist');
        if(!$validate->check($data)) {
            return ['status'=>1001,'msg'=>$validate->getError()];
        }
        
        $_DATA = [
            'vol_title'   => $data['vol_title'],
            'vol_content' => $data['vol_content'],
            'vol_status'  => isset($data['vol_status']) ? $data['vol_status'] : 1,
            'vol_addtime' => date("Y-m-d H:i:s"),
        ];
        
        $res = $this->insertGetId($_DATA);
        if($res){
            return ['status'=>1,'msg'=>'添加语音信息成功！','id' => $res];
        }else{
            return ['status'=>1003,'msg'=>'添加语音信息失败!'];
        }
    }
    
    public function Edit($data, $id){
        if(empty($id)) return ['status'=>1001,'msg'=> "ID值不能为空！"];
        
        $validate = \think\Loader::validate('Voicelist');
        if(!$validate->check($data)) {
            return ['status'=>1001,'msg'=>$validate->getError()];
        }
        
        $res = $this->GetOne($id);
        if(empty($res)) return ['status'=>1002,'msg'=> "数据不存在！"];
        
        $_DATA = [
            'vol_title'      => $data['vol_title'],
            'vol_content'    => $data['vol_content'],
            'vol_updatetime' => date("Y-m-d H:i:s"),
        ];
        
        $res = $this->where("vol_id = {$id}")->update($_DATA);
        if($res){
            return ['status'=>1,'msg'=>'修改语音信息成功！','id' => $res];
        }else{
            return ['status'=>1005,'msg'=>'修改语音信息失败!'];
        }
    }
    
    public function Del($id){
        if(empty($id)) return ['status'=>1001,'msg'=> "ID值不能为空！"];
        $res = $this->GetOne($id);
        if(empty($res)) return ['status'=>1002,'msg'=> "数据不存在！"];
        
        $resflag = $this->where("vol_id = {$id}")->delete();
        if($resflag){
            return ['status'=>1,'msg'=>'删除语音信息成功！','id' => $id];
        }else{
            return ['status'=>1005,'msg'=>'删除语音信息失败!'];
        }
    }
    
    public function Status($id, $status){
        if(empty($id)) return ['status'=>1001,'msg'=> "ID值不能为空！"];
        $res = $this->GetOne($id);
        if(empty($res)) return ['status'=>1002,'msg'=> "数据不存在！"];
        
        $status = ($status == 1) ? 2 : 1;
        
        $data = ['vol_status' => $status];
        $res = $this->where("vol_id = {$id}")->update($data);
        if($res){
            return ['status'=>1,'msg'=>'修改语音状态成功！','id' => $res];
        }else{
            return ['status'=>1005,'msg'=>'修改语音状态失败!'];
        }
    }
}