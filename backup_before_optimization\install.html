<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统安装向导</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .install-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
        }
        
        .install-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .install-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .install-header p {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .install-button {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .install-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .install-button:active {
            transform: translateY(0);
        }
        
        .install-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .result-area {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .result-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .result-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #666;
            margin: 20px 0;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .debug-tools {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
        }
        
        .debug-link {
            color: #667eea;
            text-decoration: none;
            font-size: 12px;
            margin: 0 10px;
        }
        
        .debug-link:hover {
            text-decoration: underline;
        }
        
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>🚀 系统安装向导</h1>
            <p>请配置数据库连接信息以完成系统安装</p>
        </div>
        
        <form id="install-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="hostname">数据库主机</label>
                    <input type="text" id="hostname" name="hostname" value="localhost" required>
                </div>
                <div class="form-group">
                    <label for="hostport">端口</label>
                    <input type="number" id="hostport" name="hostport" value="3306" required>
                </div>
            </div>
            
            <div class="form-group">
                <label for="database">数据库名称</label>
                <input type="text" id="database" name="database" placeholder="请输入数据库名称" required>
            </div>
            
            <div class="form-group">
                <label for="username">数据库用户名</label>
                <input type="text" id="username" name="username" placeholder="请输入数据库用户名" required>
            </div>
            
            <div class="form-group">
                <label for="password">数据库密码</label>
                <input type="password" id="password" name="password" placeholder="请输入数据库密码">
            </div>
            
            <button type="button" class="install-button" onclick="startInstall()">
                开始安装
            </button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            正在安装系统，请稍候...
        </div>
        
        <div id="result"></div>
        
        <div class="debug-tools">
            <a href="php_version_check.php" target="_blank" class="debug-link">🔍 PHP版本检查</a>
            <a href="debug_install.php" target="_blank" class="debug-link">🔧 调试工具</a>
            <a href="complete_database.php" target="_blank" class="debug-link">📊 数据库补全</a>
            <a href="test_validator_fix.php" target="_blank" class="debug-link">🧪 验证器测试</a>
        </div>
    </div>

    <script>
        let isInstalling = false;
        
        function startInstall() {
            if (isInstalling) return;
            
            // 获取表单数据
            const formData = new FormData();
            formData.append('hostname', document.getElementById('hostname').value.trim());
            formData.append('hostport', document.getElementById('hostport').value.trim());
            formData.append('database', document.getElementById('database').value.trim());
            formData.append('username', document.getElementById('username').value.trim());
            formData.append('password', document.getElementById('password').value);
            
            // 验证必填字段
            if (!formData.get('database') || !formData.get('username')) {
                showResult('数据库名称和用户名不能为空', 'error');
                return;
            }
            
            // 开始安装
            isInstalling = true;
            document.querySelector('.install-button').disabled = true;
            document.getElementById('loading').classList.add('show');
            document.getElementById('result').innerHTML = '';
            
            console.log('开始安装，使用纯JSON API...');
            
            fetch('install_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);
                
                // 检查Content-Type
                const contentType = response.headers.get('Content-Type');
                console.log('Content-Type:', contentType);
                
                if (!contentType || !contentType.includes('application/json')) {
                    console.warn('响应不是JSON格式:', contentType);
                }
                
                return response.text();
            })
            .then(responseText => {
                console.log('原始响应文本:', responseText);
                
                try {
                    const result = JSON.parse(responseText);
                    console.log('解析后的响应:', result);
                    
                    if (result.status) {
                        showInstallSuccess(result);
                    } else {
                        showResult('安装失败: ' + result.message, 'error');
                    }
                } catch (e) {
                    console.error('JSON解析错误:', e);
                    console.error('原始响应:', responseText);
                    showResult('JSON解析错误: ' + e.message + '\n\n原始响应:\n' + responseText, 'error');
                }
            })
            .catch(error => {
                console.error('请求错误:', error);
                showResult('网络请求失败: ' + error.message, 'error');
            })
            .finally(() => {
                isInstalling = false;
                document.querySelector('.install-button').disabled = false;
                document.getElementById('loading').classList.remove('show');
            });
        }
        
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result-area result-${type}">${message}</div>`;
        }
        
        function showInstallSuccess(result) {
            let successMessage = '<h3>🎉 安装成功！</h3>';
            successMessage += '<p>' + result.message + '</p>';
            
            if (result.data) {
                successMessage += '<br><strong>安装详情：</strong><br>';
                successMessage += '• 创建表数量: ' + (result.data.tables_created || 0) + '<br>';
                successMessage += '• 插入数据条数: ' + (result.data.data_inserted || 0) + '<br>';
                
                if (result.data.admin_account) {
                    successMessage += '<br><strong>默认管理员账号：</strong><br>';
                    successMessage += '• 用户名: <code>' + result.data.admin_account + '</code><br>';
                    successMessage += '• 密码: <code>' + result.data.admin_password + '</code><br>';
                }
            }
            
            successMessage += '<br><p><strong>下一步操作：</strong></p>';
            successMessage += '<p>1. 请记住管理员账号信息</p>';
            successMessage += '<p>2. <a href="/" target="_blank" style="color: #667eea;">点击此处登录系统</a></p>';
            successMessage += '<p>3. 建议删除安装相关文件以提高安全性</p>';
            
            showResult(successMessage, 'success');
        }
        
        // 表单回车提交
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !isInstalling) {
                startInstall();
            }
        });
        
        // 页面加载完成后检查是否已安装
        document.addEventListener('DOMContentLoaded', function() {
            fetch('install_api.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'check=1'
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === false && result.message.includes('已经安装')) {
                    showResult('系统已经安装完成。如需重新安装，请删除 install.lock 文件。<br><br><a href="/" target="_blank" style="color: #667eea;">点击此处访问系统</a>', 'info');
                    document.querySelector('.install-button').disabled = true;
                }
            })
            .catch(e => {
                console.log('检查安装状态时出错:', e);
            });
        });
    </script>
</body>
</html>