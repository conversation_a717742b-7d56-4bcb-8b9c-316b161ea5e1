<?php
/**
 * JSON错误追踪工具
 * 精确定位"Unexpected token '<'"错误的发生步骤
 */

echo "=== JSON错误追踪分析 ===\n\n";

echo "问题分析：\"Unexpected token '<', ... is not valid JSON\"\n";
echo "这个错误通常发生在以下情况：\n";
echo "1. API返回HTML错误页面而不是JSON\n";
echo "2. PHP错误输出污染了JSON响应\n";
echo "3. 服务器配置问题导致错误页面\n\n";

// 模拟浏览器请求来追踪错误
echo "=== 模拟浏览器请求追踪 ===\n";

$testData = [
    'step' => 'install',
    'hostname' => 'localhost',
    'database' => 'test_trace_db',
    'username' => 'root',
    'password' => '',
    'hostport' => '3306'
];

$postData = http_build_query($testData);

// 测试不同的API端点
$endpoints = [
    'install_api_pdo.php' => '当前使用的PDO API',
    'install_api.php' => '原始mysqli API',
    'simple_install.php' => '简化安装脚本'
];

foreach ($endpoints as $endpoint => $description) {
    echo "\n--- 测试 $endpoint ($description) ---\n";
    
    if (!file_exists($endpoint)) {
        echo "❌ 文件不存在\n";
        continue;
    }
    
    // 创建HTTP上下文
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                "Content-Type: application/x-www-form-urlencoded",
                "Content-Length: " . strlen($postData),
                "User-Agent: Error-Tracer/1.0"
            ],
            'content' => $postData,
            'timeout' => 10
        ]
    ]);
    
    // 发送请求并捕获响应
    $response = @file_get_contents("http://localhost/$endpoint", false, $context);
    $headers = $http_response_header ?? [];
    
    if ($response === false) {
        echo "❌ 请求失败，尝试直接包含文件...\n";
        
        // 直接包含文件测试
        $_POST = $testData;
        $_SERVER['REQUEST_METHOD'] = 'POST';
        
        ob_start();
        try {
            include $endpoint;
        } catch (Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
        } catch (Error $e) {
            echo "Error: " . $e->getMessage() . "\n";
        }
        $response = ob_get_contents();
        ob_end_clean();
        
        unset($_POST);
        unset($_SERVER['REQUEST_METHOD']);
    }
    
    echo "响应长度: " . strlen($response) . " bytes\n";
    
    if (empty($response)) {
        echo "❌ 响应为空\n";
        continue;
    }
    
    // 分析响应内容
    $firstChar = substr(trim($response), 0, 1);
    echo "首字符: '$firstChar'\n";
    
    if ($firstChar === '<') {
        echo "❌ 检测到HTML响应（这就是'Unexpected token <'的原因）\n";
        
        // 分析HTML内容
        if (strpos($response, '<!DOCTYPE html') !== false) {
            echo "   类型: 完整HTML页面\n";
        } elseif (strpos($response, '<html') !== false) {
            echo "   类型: HTML文档\n";
        } elseif (strpos($response, '<br') !== false) {
            echo "   类型: PHP错误输出\n";
        }
        
        // 查找常见错误模式
        if (strpos($response, 'Fatal error') !== false) {
            echo "   ❌ 包含PHP致命错误\n";
            preg_match('/Fatal error:([^<]+)/', $response, $matches);
            if ($matches) {
                echo "   错误详情: " . trim($matches[1]) . "\n";
            }
        }
        
        if (strpos($response, 'Call to undefined function') !== false) {
            echo "   ❌ 包含未定义函数错误\n";
            preg_match('/Call to undefined function ([^(]+)/', $response, $matches);
            if ($matches) {
                echo "   未定义函数: " . trim($matches[1]) . "\n";
            }
        }
        
        if (strpos($response, 'mysqli_') !== false) {
            echo "   ❌ 包含mysqli相关错误\n";
        }
        
        // 显示响应的前200字符
        echo "   响应前200字符:\n";
        echo "   " . substr($response, 0, 200) . "\n";
        
    } elseif ($firstChar === '{') {
        echo "✅ 检测到JSON响应\n";
        
        // 尝试解析JSON
        $json = json_decode($response, true);
        if ($json === null) {
            echo "❌ JSON解析失败: " . json_last_error_msg() . "\n";
            echo "   可能的原因: JSON格式错误或包含非法字符\n";
        } else {
            echo "✅ JSON解析成功\n";
            echo "   状态: " . ($json['status'] ? '成功' : '失败') . "\n";
            echo "   消息: " . $json['message'] . "\n";
        }
    } else {
        echo "⚠️ 未知响应格式\n";
        echo "   响应前100字符: " . substr($response, 0, 100) . "\n";
    }
}

echo "\n=== 错误源头分析 ===\n";

// 检查可能的错误源头
echo "1. 检查PHP错误设置\n";
echo "   display_errors: " . (ini_get('display_errors') ? '开启' : '关闭') . "\n";
echo "   error_reporting: " . error_reporting() . "\n";
echo "   log_errors: " . (ini_get('log_errors') ? '开启' : '关闭') . "\n";

echo "\n2. 检查扩展状态\n";
echo "   mysqli: " . (extension_loaded('mysqli') ? '✅ 已加载' : '❌ 未加载') . "\n";
echo "   pdo: " . (extension_loaded('pdo') ? '✅ 已加载' : '❌ 未加载') . "\n";
echo "   pdo_mysql: " . (extension_loaded('pdo_mysql') ? '✅ 已加载' : '❌ 未加载') . "\n";

echo "\n3. 检查文件完整性\n";
$files = ['install_api_pdo.php', 'install_api.php', 'simple_install.php', 'setup.html'];
foreach ($files as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "   $file: ✅ 存在 ($size bytes)\n";
    } else {
        echo "   $file: ❌ 不存在\n";
    }
}

echo "\n=== 结论和建议 ===\n";

if (!extension_loaded('mysqli')) {
    echo "🎯 主要问题: mysqli扩展未加载\n";
    echo "   这会导致install_api.php和simple_install.php中的mysqli_connect()函数未定义\n";
    echo "   当函数未定义时，PHP会输出错误页面而不是JSON响应\n";
    echo "   解决方案: 使用install_api_pdo.php（已经在setup.html中配置）\n\n";
}

echo "🔍 \"Unexpected token '<'\" 错误的真正原因:\n";
echo "1. 当API脚本遇到错误时，PHP输出HTML错误页面\n";
echo "2. JavaScript尝试解析HTML作为JSON，遇到'<'字符\n";
echo "3. JSON.parse()抛出\"Unexpected token '<'\"错误\n\n";

echo "✅ 推荐解决方案:\n";
echo "1. 使用install_api_pdo.php（不依赖mysqli扩展）\n";
echo "2. 确保数据库连接信息正确\n";
echo "3. 使用mysql_setup_helper.php配置数据库连接\n";
echo "4. 使用setup_fixed.html进行安装（包含连接测试功能）\n";

echo "\n=== 追踪完成 ===\n";
?>
