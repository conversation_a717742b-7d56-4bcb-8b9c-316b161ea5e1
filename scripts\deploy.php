<?php
/**
 * 系统部署和维护脚本
 * 用于生产环境的部署、更新和维护
 */

class DeployManager {
    
    private $config;
    
    public function __construct(){
        $this->config = [
            'backup_path' => './backups/',
            'log_path' => './runtime/deploy/',
            'maintenance_mode' => false
        ];
        
        // 确保目录存在
        $this->ensureDirectories();
    }
    
    /**
     * 确保必要目录存在
     */
    private function ensureDirectories(){
        $dirs = [$this->config['backup_path'], $this->config['log_path']];
        foreach($dirs as $dir){
            if(!is_dir($dir)){
                mkdir($dir, 0755, true);
            }
        }
    }
    
    /**
     * 数据库备份
     */
    public function backupDatabase(){
        try {
            require_once './config/database.php';
            $dbConfig = include './config/database.php';
            
            $backupFile = $this->config['backup_path'] . 'db_backup_' . date('Y-m-d_H-i-s') . '.sql';
            
            $command = sprintf(
                'mysqldump -h%s -P%s -u%s -p%s %s > %s',
                $dbConfig['hostname'],
                $dbConfig['hostport'] ?: 3306,
                $dbConfig['username'],
                $dbConfig['password'],
                $dbConfig['database'],
                $backupFile
            );
            
            exec($command, $output, $returnCode);
            
            if($returnCode === 0){
                $this->log("数据库备份成功: {$backupFile}");
                return ['status' => true, 'file' => $backupFile];
            } else {
                $this->log("数据库备份失败", 'error');
                return ['status' => false, 'message' => '备份命令执行失败'];
            }
            
        } catch (Exception $e) {
            $this->log("数据库备份异常: " . $e->getMessage(), 'error');
            return ['status' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 清理系统缓存
     */
    public function clearCache(){
        try {
            $cacheDir = './runtime/cache/';
            $tempDir = './runtime/temp/';
            $logDir = './runtime/log/';
            
            $this->removeDirectory($cacheDir);
            $this->removeDirectory($tempDir);
            
            // 保留最近7天的日志
            $this->cleanOldLogs($logDir, 7);
            
            $this->log("系统缓存清理完成");
            return ['status' => true, 'message' => '缓存清理完成'];
            
        } catch (Exception $e) {
            $this->log("缓存清理失败: " . $e->getMessage(), 'error');
            return ['status' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 系统健康检查
     */
    public function healthCheck(){
        require_once './application/common/service/SystemMonitor.php';
        return \app\common\service\SystemMonitor::healthCheck();
    }
    
    /**
     * 数据库优化
     */
    public function optimizeDatabase(){
        try {
            require_once './config/database.php';
            $dbConfig = include './config/database.php';
            
            $pdo = new PDO(
                "mysql:host={$dbConfig['hostname']};dbname={$dbConfig['database']};charset=utf8mb4",
                $dbConfig['username'],
                $dbConfig['password']
            );
            
            // 获取所有web_开头的表
            $tables = $pdo->query("SHOW TABLES LIKE 'web_%'")->fetchAll(PDO::FETCH_COLUMN);
            
            $optimized = [];
            foreach($tables as $table){
                $pdo->exec("OPTIMIZE TABLE `{$table}`");
                $pdo->exec("ANALYZE TABLE `{$table}`");
                $optimized[] = $table;
            }
            
            $this->log("数据库优化完成，优化表数: " . count($optimized));
            return ['status' => true, 'tables' => $optimized];
            
        } catch (Exception $e) {
            $this->log("数据库优化失败: " . $e->getMessage(), 'error');
            return ['status' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 删除目录及其内容
     */
    private function removeDirectory($dir){
        if(!is_dir($dir)) return;
        
        $files = array_diff(scandir($dir), ['.', '..']);
        foreach($files as $file){
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->removeDirectory($path) : unlink($path);
        }
        rmdir($dir);
    }
    
    /**
     * 清理旧日志
     */
    private function cleanOldLogs($logDir, $keepDays){
        if(!is_dir($logDir)) return;
        
        $cutoffTime = time() - ($keepDays * 24 * 3600);
        $files = glob($logDir . '*');
        
        foreach($files as $file){
            if(is_file($file) && filemtime($file) < $cutoffTime){
                unlink($file);
            }
        }
    }
    
    /**
     * 记录日志
     */
    private function log($message, $level = 'info'){
        $logFile = $this->config['log_path'] . 'deploy_' . date('Y-m-d') . '.log';
        $logEntry = date('Y-m-d H:i:s') . " [{$level}] {$message}\n";
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}

// 命令行调用支持
if(php_sapi_name() === 'cli'){
    $deploy = new DeployManager();
    
    $action = $argv[1] ?? 'help';
    
    switch($action){
        case 'backup':
            echo "正在备份数据库...\n";
            $result = $deploy->backupDatabase();
            echo $result['status'] ? "备份成功!\n" : "备份失败: {$result['message']}\n";
            break;
            
        case 'cache':
            echo "正在清理缓存...\n";
            $result = $deploy->clearCache();
            echo $result['status'] ? "缓存清理完成!\n" : "清理失败: {$result['message']}\n";
            break;
            
        case 'optimize':
            echo "正在优化数据库...\n";
            $result = $deploy->optimizeDatabase();
            echo $result['status'] ? "数据库优化完成!\n" : "优化失败: {$result['message']}\n";
            break;
            
        case 'health':
            echo "正在检查系统健康状态...\n";
            $result = $deploy->healthCheck();
            print_r($result);
            break;
            
        default:
            echo "系统部署管理工具\n\n";
            echo "使用方法:\n";
            echo "  php scripts/deploy.php backup   - 备份数据库\n";
            echo "  php scripts/deploy.php cache    - 清理系统缓存\n";
            echo "  php scripts/deploy.php optimize - 优化数据库\n";
            echo "  php scripts/deploy.php health   - 系统健康检查\n";
    }
}
?>