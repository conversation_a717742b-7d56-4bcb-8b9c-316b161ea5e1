<?php
/**
 * 测试SQL文件解析和执行
 */

header("Content-type: text/html; charset=utf-8");

echo "<h1>SQL文件解析测试</h1>";

$sqlFile = 'database_complete_init.sql';

if (!file_exists($sqlFile)) {
    echo "<div style='color: red;'>❌ SQL文件不存在: $sqlFile</div>";
    exit;
}

$sql = file_get_contents($sqlFile);
echo "<p><strong>SQL文件大小:</strong> " . strlen($sql) . " bytes</p>";

// 当前的简单分割方式（simple_install.php中使用的方法）
echo "<h2>1. 当前简单分割方式测试</h2>";
$statements = explode(';', $sql);
echo "<p><strong>分割后语句数量:</strong> " . count($statements) . "</p>";

$validStatements = 0;
$emptyStatements = 0;
$setStatements = 0;
$commentStatements = 0;

foreach($statements as $index => $statement){
    $statement = trim($statement);
    
    if (empty($statement)) {
        $emptyStatements++;
        continue;
    }
    
    if (strpos($statement, 'SET') === 0) {
        $setStatements++;
        continue;
    }
    
    if (strpos($statement, '--') === 0) {
        $commentStatements++;
        continue;
    }
    
    $validStatements++;
    
    // 显示前5个有效语句
    if ($validStatements <= 5) {
        echo "<div style='background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 4px;'>";
        echo "<strong>语句 $validStatements:</strong><br>";
        echo "<code>" . htmlspecialchars(substr($statement, 0, 100)) . (strlen($statement) > 100 ? '...' : '') . "</code>";
        echo "</div>";
    }
}

echo "<div style='background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 4px;'>";
echo "<strong>分析结果:</strong><br>";
echo "- 总语句数: " . count($statements) . "<br>";
echo "- 有效语句: $validStatements<br>";
echo "- 空语句: $emptyStatements<br>";
echo "- SET语句: $setStatements<br>";
echo "- 注释语句: $commentStatements<br>";
echo "</div>";

// 检查是否有多行CREATE TABLE语句被错误分割
echo "<h2>2. 多行语句检查</h2>";
$createTableCount = 0;
$problematicStatements = [];

foreach($statements as $index => $statement){
    $statement = trim($statement);
    
    if (empty($statement) || strpos($statement, 'SET') === 0 || strpos($statement, '--') === 0) {
        continue;
    }
    
    if (strpos(strtoupper($statement), 'CREATE TABLE') !== false) {
        $createTableCount++;
        
        // 检查是否是完整的CREATE TABLE语句
        if (substr_count($statement, '(') !== substr_count($statement, ')')) {
            $problematicStatements[] = [
                'index' => $index,
                'preview' => substr($statement, 0, 200) . '...'
            ];
        }
    }
}

echo "<p><strong>CREATE TABLE语句数量:</strong> $createTableCount</p>";

if (!empty($problematicStatements)) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 4px; color: #721c24;'>";
    echo "<strong>❌ 发现问题语句 (括号不匹配):</strong><br>";
    foreach($problematicStatements as $problem) {
        echo "语句 {$problem['index']}: " . htmlspecialchars($problem['preview']) . "<br>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 4px; color: #155724;'>";
    echo "✅ 所有CREATE TABLE语句括号匹配正常";
    echo "</div>";
}

// 改进的分割方式
echo "<h2>3. 改进的分割方式测试</h2>";

function parseSQL($sql) {
    $statements = [];
    $current = '';
    $inQuotes = false;
    $quoteChar = '';
    $length = strlen($sql);
    
    for ($i = 0; $i < $length; $i++) {
        $char = $sql[$i];
        
        // 处理引号
        if (($char === '"' || $char === "'") && !$inQuotes) {
            $inQuotes = true;
            $quoteChar = $char;
        } elseif ($char === $quoteChar && $inQuotes) {
            $inQuotes = false;
            $quoteChar = '';
        }
        
        // 分号分割（但不在引号内）
        if ($char === ';' && !$inQuotes) {
            $statement = trim($current);
            if (!empty($statement)) {
                $statements[] = $statement;
            }
            $current = '';
            continue;
        }
        
        $current .= $char;
    }
    
    // 添加最后一个语句（如果有）
    $statement = trim($current);
    if (!empty($statement)) {
        $statements[] = $statement;
    }
    
    return $statements;
}

$improvedStatements = parseSQL($sql);
$improvedValid = 0;

foreach($improvedStatements as $statement) {
    $statement = trim($statement);
    if (empty($statement) || strpos($statement, 'SET') === 0 || strpos($statement, '--') === 0) {
        continue;
    }
    $improvedValid++;
}

echo "<div style='background: #d1ecf1; padding: 15px; margin: 10px 0; border-radius: 4px;'>";
echo "<strong>改进方式结果:</strong><br>";
echo "- 总语句数: " . count($improvedStatements) . "<br>";
echo "- 有效语句: $improvedValid<br>";
echo "</div>";

echo "<h2>4. 建议的修复方案</h2>";
echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 4px;'>";
echo "<strong>问题分析:</strong><br>";
echo "1. 简单的explode(';', \$sql)可能会错误分割包含分号的字符串<br>";
echo "2. 没有处理SQL注释和空行<br>";
echo "3. 没有验证语句的完整性<br><br>";

echo "<strong>修复建议:</strong><br>";
echo "1. 使用改进的SQL解析函数<br>";
echo "2. 添加语句完整性验证<br>";
echo "3. 增加详细的错误日志<br>";
echo "4. 支持多语句执行失败的回滚机制";
echo "</div>";
?>