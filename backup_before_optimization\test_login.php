<?php
/**
 * 登录功能测试脚本
 */

// 模拟登录测试
header("Content-type: text/html; charset=utf-8");

echo "<h2>登录功能测试</h2>";

// 1. 检查数据库连接
try {
    $dbConfig = include 'config/database.php';
    echo "✓ 数据库配置文件存在<br>";
    
    $pdo = new PDO(
        "mysql:host={$dbConfig['hostname']};dbname={$dbConfig['database']};charset=utf8mb4",
        $dbConfig['username'],
        $dbConfig['password']
    );
    echo "✓ 数据库连接成功<br>";
    
    // 2. 检查管理员账户是否存在
    $stmt = $pdo->prepare("SELECT * FROM web_users WHERE u_phone = 'admin'");
    $stmt->execute();
    $user = $stmt->fetch();
    
    if($user){
        echo "✓ 管理员账户存在<br>";
        echo "用户信息: {$user['u_nickname']} (状态: {$user['u_status']})<br>";
        
        // 3. 测试密码验证
        $testPassword = md5('admin123456');
        if($user['u_password'] === $testPassword){
            echo "✓ 密码验证正确<br>";
        } else {
            echo "✗ 密码不匹配<br>";
            echo "数据库密码: {$user['u_password']}<br>";
            echo "测试密码: {$testPassword}<br>";
        }
    } else {
        echo "✗ 管理员账户不存在<br>";
        
        // 尝试创建管理员账户
        $stmt = $pdo->prepare("INSERT INTO web_users (u_phone, u_password, u_nickname, u_status, u_supermanage, u_regtime) VALUES ('admin', MD5('admin123456'), '系统管理员', 1, 2, NOW())");
        if($stmt->execute()){
            echo "✓ 管理员账户创建成功<br>";
        } else {
            echo "✗ 管理员账户创建失败<br>";
        }
    }
    
} catch (Exception $e) {
    echo "✗ 数据库连接失败: " . $e->getMessage() . "<br>";
}

// 4. 检查ThinkPHP环境
echo "<hr>";
echo "ThinkPHP 环境检查:<br>";
echo "PHP版本: " . PHP_VERSION . "<br>";
echo "文档根目录: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "当前脚本路径: " . __FILE__ . "<br>";

// 5. 测试AJAX请求路径
echo "<hr>";
echo "测试登录请求:<br>";
echo "<button onclick='testLogin()'>测试登录AJAX</button>";
echo "<div id='result'></div>";

echo "<script src='/template/layuiadmin/layui/layui.js'></script>";
echo "<script>
function testLogin(){
    var xhr = new XMLHttpRequest();
    xhr.open('POST', '/website/index/Login', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
    
    xhr.onload = function(){
        document.getElementById('result').innerHTML = '响应状态: ' + xhr.status + '<br>响应内容: ' + xhr.responseText;
    };
    
    xhr.onerror = function(){
        document.getElementById('result').innerHTML = '请求失败';
    };
    
    xhr.send('username=admin&password=admin123456');
}
</script>";
?>