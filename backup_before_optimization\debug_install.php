<?php
/**
 * 安装调试工具 - 查看具体错误信息
 */

header("Content-type: text/html; charset=utf-8");
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>安装调试工具</h1>";
echo "<p>用于调试 'Unexpected token' JSON解析错误</p>";

// 模拟安装请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>POST请求调试</h2>";
    
    try {
        $config = [
            'hostname' => $_POST['hostname'] ?? 'localhost',
            'database' => $_POST['database'] ?? '',
            'username' => $_POST['username'] ?? '',
            'password' => $_POST['password'] ?? '',
            'hostport' => $_POST['hostport'] ?? '3306'
        ];
        
        echo "<h3>接收到的配置:</h3>";
        echo "<pre>";
        print_r($config);
        echo "</pre>";
        
        // 测试数据库连接
        echo "<h3>数据库连接测试:</h3>";
        $link = mysqli_connect(
            $config['hostname'],
            $config['username'], 
            $config['password'],
            '',
            $config['hostport']
        );
        
        if(!$link){
            throw new Exception('数据库连接失败: ' . mysqli_connect_error());
        }
        echo "✓ 数据库连接成功<br>";
        
        mysqli_set_charset($link, 'utf8mb4');
        
        // 创建数据库
        $sql = "CREATE DATABASE IF NOT EXISTS `{$config['database']}` DEFAULT CHARSET utf8mb4";
        if(!mysqli_query($link, $sql)){
            throw new Exception('创建数据库失败: ' . mysqli_error($link));
        }
        echo "✓ 数据库创建/选择成功<br>";
        
        // 选择数据库
        if(!mysqli_select_db($link, $config['database'])){
            throw new Exception('选择数据库失败: ' . mysqli_error($link));
        }
        
        // 测试简单的表创建
        $testSql = "CREATE TABLE IF NOT EXISTS `test_table` (`id` int(11) NOT NULL AUTO_INCREMENT, PRIMARY KEY (`id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        if(!mysqli_query($link, $testSql)){
            throw new Exception('测试表创建失败: ' . mysqli_error($link));
        }
        echo "✓ 测试表创建成功<br>";
        
        // 删除测试表
        mysqli_query($link, "DROP TABLE IF EXISTS `test_table`");
        
        mysqli_close($link);
        
        echo "<h3>返回标准JSON:</h3>";
        $result = ['status' => true, 'message' => '调试测试成功'];
        
        // 不要有任何输出污染JSON
        ob_clean();
        header('Content-Type: application/json');
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
        exit;
        
    } catch (Exception $e) {
        echo "<h3>错误信息:</h3>";
        echo "<div style='color: red; border: 1px solid red; padding: 10px;'>";
        echo "<strong>错误:</strong> " . $e->getMessage() . "<br>";
        echo "<strong>文件:</strong> " . $e->getFile() . ":" . $e->getLine() . "<br>";
        echo "</div>";
        
        // 返回错误JSON
        ob_clean();
        header('Content-Type: application/json');
        echo json_encode(['status' => false, 'message' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        exit;
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>安装调试工具</title>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 300px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        button { padding: 10px 20px; background: #007cff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h2>数据库配置测试</h2>
    
    <form id="debug-form">
        <div class="form-group">
            <label>数据库主机:</label>
            <input type="text" name="hostname" value="localhost" required>
        </div>
        
        <div class="form-group">
            <label>数据库端口:</label>
            <input type="text" name="hostport" value="3306" required>
        </div>
        
        <div class="form-group">
            <label>数据库名:</label>
            <input type="text" name="database" placeholder="请输入数据库名" required>
        </div>
        
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" name="username" placeholder="请输入数据库用户名" required>
        </div>
        
        <div class="form-group">
            <label>密码:</label>
            <input type="password" name="password" placeholder="请输入数据库密码">
        </div>
        
        <div class="form-group">
            <button type="button" onclick="testInstall()">调试测试</button>
            <button type="button" onclick="testOriginalInstall()">测试原始安装脚本</button>
            <button type="button" onclick="testNewAPI()">测试新JSON API</button>
        </div>
    </form>
    
    <div id="result"></div>

    <script>
    function testInstall() {
        var formData = new FormData(document.getElementById('debug-form'));
        
        $('#result').html('正在测试...');
        
        $.ajax({
            type: "POST",
            url: window.location.href,
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function(xhr) {
                console.log('开始调试测试...');
            },
            success: function(response, textStatus, xhr) {
                console.log('原始响应:', xhr.responseText);
                console.log('解析后响应:', response);
                
                if (typeof response === 'string') {
                    try {
                        response = JSON.parse(response);
                    } catch (e) {
                        $('#result').html('<div class="result error">响应不是有效JSON:<br><pre>' + xhr.responseText + '</pre></div>');
                        return;
                    }
                }
                
                if (response.status) {
                    $('#result').html('<div class="result success">调试测试成功: ' + response.message + '</div>');
                } else {
                    $('#result').html('<div class="result error">调试测试失败: ' + response.message + '</div>');
                }
            },
            error: function(xhr, textStatus, errorThrown) {
                console.log('AJAX错误:', textStatus, errorThrown);
                console.log('响应状态:', xhr.status);
                console.log('响应文本:', xhr.responseText);
                
                $('#result').html('<div class="result error">' +
                    '<strong>AJAX错误:</strong> ' + textStatus + '<br>' +
                    '<strong>状态码:</strong> ' + xhr.status + '<br>' +
                    '<strong>响应内容:</strong><br>' +
                    '<pre>' + xhr.responseText + '</pre>' +
                    '</div>');
            }
        });
    }
    
    function testOriginalInstall() {
        var formData = $('#debug-form').serialize();
        formData += '&step=install';
        
        $('#result').html('正在测试原始安装脚本...');
        
        $.ajax({
            type: "POST",
            url: "/simple_install.php",
            data: formData,
            dataType: "text", // 接收原始文本，手动解析
            success: function(responseText, textStatus, xhr) {
                console.log('原始响应文本:', responseText);
                console.log('响应头 Content-Type:', xhr.getResponseHeader('Content-Type'));
                
                try {
                    var response = JSON.parse(responseText);
                    if (response.status) {
                        $('#result').html('<div class="result success">原始安装成功: ' + response.message + '</div>');
                    } else {
                        $('#result').html('<div class="result error">原始安装失败: ' + response.message + '</div>');
                    }
                } catch (e) {
                    $('#result').html('<div class="result error">' +
                        '<strong>JSON解析错误:</strong> ' + e.message + '<br>' +
                        '<strong>原始响应:</strong><br>' +
                        '<pre>' + responseText + '</pre>' +
                        '</div>');
                }
            },
            error: function(xhr, textStatus, errorThrown) {
                $('#result').html('<div class="result error">' +
                    '<strong>请求失败:</strong> ' + textStatus + '<br>' +
                    '<strong>状态码:</strong> ' + xhr.status + '<br>' +
                    '<strong>响应内容:</strong><br>' +
                    '<pre>' + xhr.responseText + '</pre>' +
                    '</div>');
            }
        });
    }
    
    function testNewAPI() {
        var formData = new FormData(document.getElementById('debug-form'));
        
        $('#result').html('正在测试新的JSON API...');
        
        $.ajax({
            type: "POST",
            url: "install_api.php",
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function(xhr) {
                console.log('开始测试新API...');
            },
            success: function(response, textStatus, xhr) {
                console.log('API响应状态:', xhr.status);
                console.log('API Content-Type:', xhr.getResponseHeader('Content-Type'));
                console.log('API原始响应:', xhr.responseText);
                console.log('API解析后响应:', response);
                
                if (typeof response === 'string') {
                    try {
                        response = JSON.parse(response);
                    } catch (e) {
                        $('#result').html('<div class="result error"><strong>JSON解析失败:</strong> ' + e.message + '<br><strong>原始响应:</strong><br><pre>' + xhr.responseText + '</pre></div>');
                        return;
                    }
                }
                
                if (response.status) {
                    var successHtml = '<div class="result success"><strong>新API测试成功:</strong> ' + response.message;
                    if (response.data) {
                        successHtml += '<br><br><strong>详细信息:</strong><br>';
                        successHtml += '创建表数: ' + (response.data.tables_created || 0) + '<br>';
                        successHtml += '插入数据: ' + (response.data.data_inserted || 0) + '<br>';
                        if (response.data.admin_account) {
                            successHtml += '管理员账号: ' + response.data.admin_account + '<br>';
                            successHtml += '管理员密码: ' + response.data.admin_password + '<br>';
                        }
                    }
                    successHtml += '</div>';
                    $('#result').html(successHtml);
                } else {
                    $('#result').html('<div class="result error"><strong>新API测试失败:</strong> ' + response.message + '</div>');
                }
            },
            error: function(xhr, textStatus, errorThrown) {
                console.log('API请求错误:', textStatus, errorThrown);
                console.log('API错误状态:', xhr.status);
                console.log('API错误响应:', xhr.responseText);
                
                $('#result').html('<div class="result error">' +
                    '<strong>API请求失败:</strong> ' + textStatus + '<br>' +
                    '<strong>状态码:</strong> ' + xhr.status + '<br>' +
                    '<strong>响应内容:</strong><br>' +
                    '<pre>' + xhr.responseText + '</pre>' +
                    '</div>');
            }
        });
    }
    </script>
</body>
</html>