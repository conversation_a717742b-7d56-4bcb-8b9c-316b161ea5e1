# 系统安装指南

## 安装方式选择

### 推荐安装方式（已修复A级错误问题）

1. **setup.html** - 简化安装界面
2. **simple_install.php** - 使用mysqli的稳定安装
3. **install.php** - 完整功能安装向导

## 自动修复的问题

### IP白名单自动配置
- 安装完成后自动关闭IP白名单功能
- 自动添加当前域名到白名单列表
- 避免安装后出现"系统A级错误"

### 自动创建目录
- runtime/ （运行时缓存）
- upload/ （文件上传）
- config/extra/ （扩展配置）

### 默认安全设置
- 管理员账户: admin/admin123456
- 调试模式: 开启（开发环境）
- IP白名单: 关闭（避免访问问题）

## 生产环境配置建议

安装完成后，请手动调整以下配置：

### 1. 修改默认密码
```
登录后台 → 用户管理 → 修改管理员密码
```

### 2. 启用IP白名单（可选）
编辑 `config/extra/ip.php`：
```php
'onoff' => true,  // 启用IP白名单
'ip' => "your-domain.com|backup-domain.com",  // 配置允许的域名
```

### 3. 关闭调试模式
编辑 `config/config.php`：
```php
'app_debug' => false,
```

### 4. 清理安装文件
```bash
rm install.php simple_install.php setup.html quick_fix.php
```

## 常见问题解决

### A级错误
- 原因：IP白名单限制
- 解决：运行 `php quick_fix.php` 或手动关闭IP白名单

### 数据库连接失败
- 检查 `config/database.php` 中的数据库配置
- 确认数据库服务已启动
- 验证数据库凭证正确性

### 权限问题
- 确保 runtime、upload、config 目录可写
- Linux/Unix: `chmod -R 755 runtime upload config`