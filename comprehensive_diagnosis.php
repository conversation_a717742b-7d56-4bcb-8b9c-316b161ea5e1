<?php
/**
 * 综合诊断工具
 * 全面检查PHP环境、数据库连接和安装API问题
 */

echo "=== 综合系统诊断 ===\n\n";

// 1. PHP环境检查
echo "1. PHP环境检查\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "操作系统: " . PHP_OS . "\n";

$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
$optionalExtensions = ['mysqli', 'curl', 'openssl'];

echo "\n必需扩展:\n";
foreach ($requiredExtensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "  $ext: " . ($loaded ? "✅ 已加载" : "❌ 未加载") . "\n";
}

echo "\n可选扩展:\n";
foreach ($optionalExtensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "  $ext: " . ($loaded ? "✅ 已加载" : "⚠️ 未加载") . "\n";
}

// 2. 数据库连接测试
echo "\n2. 数据库连接测试\n";

// 读取当前数据库配置
$dbConfig = null;
if (file_exists('config/database.php')) {
    $dbConfig = include 'config/database.php';
    echo "当前数据库配置:\n";
    echo "  主机: " . ($dbConfig['hostname'] ?? 'N/A') . "\n";
    echo "  数据库: " . ($dbConfig['database'] ?? 'N/A') . "\n";
    echo "  用户名: " . ($dbConfig['username'] ?? 'N/A') . "\n";
    echo "  端口: " . ($dbConfig['hostport'] ?? '3306') . "\n";
    
    // 测试当前配置
    try {
        $dsn = "mysql:host={$dbConfig['hostname']};port=" . ($dbConfig['hostport'] ?: 3306);
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5
        ]);
        echo "  连接状态: ✅ 成功\n";
        
        $version = $pdo->query('SELECT VERSION()')->fetchColumn();
        echo "  MySQL版本: $version\n";
        
    } catch (PDOException $e) {
        echo "  连接状态: ❌ 失败 - " . $e->getMessage() . "\n";
    }
} else {
    echo "数据库配置文件不存在\n";
}

// 测试常见的数据库连接配置
echo "\n测试常见数据库配置:\n";
$testConfigs = [
    ['localhost', 'root', '', '3306'],
    ['127.0.0.1', 'root', '', '3306'],
    ['localhost', 'root', 'root', '3306'],
    ['127.0.0.1', 'root', 'root', '3306']
];

foreach ($testConfigs as $config) {
    list($host, $user, $pass, $port) = $config;
    echo "  测试 $user@$host:$port ";
    
    try {
        $dsn = "mysql:host=$host;port=$port";
        $pdo = new PDO($dsn, $user, $pass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 3
        ]);
        echo "✅ 成功\n";
    } catch (PDOException $e) {
        echo "❌ 失败\n";
    }
}

// 3. 安装API测试
echo "\n3. 安装API测试\n";

$apis = ['install_api_pdo.php', 'install_api.php', 'simple_install.php'];

foreach ($apis as $api) {
    echo "测试 $api:\n";
    
    if (!file_exists($api)) {
        echo "  ❌ 文件不存在\n";
        continue;
    }
    
    // 模拟POST请求
    $_POST = [
        'step' => 'install',
        'hostname' => 'localhost',
        'database' => 'test_diagnosis_db',
        'username' => 'root',
        'password' => '',
        'hostport' => '3306'
    ];
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    ob_start();
    
    try {
        include $api;
    } catch (Exception $e) {
        echo "  ❌ 异常: " . $e->getMessage() . "\n";
    } catch (Error $e) {
        echo "  ❌ 错误: " . $e->getMessage() . "\n";
    }
    
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "  响应长度: " . strlen($output) . " bytes\n";
    
    if (empty($output)) {
        echo "  ❌ 响应为空\n";
    } else {
        $json = json_decode($output, true);
        if ($json === null) {
            echo "  ❌ JSON解析失败: " . json_last_error_msg() . "\n";
            echo "  原始响应前100字符: " . substr($output, 0, 100) . "\n";
        } else {
            echo "  ✅ JSON解析成功\n";
            echo "  状态: " . ($json['status'] ? '成功' : '失败') . "\n";
            echo "  消息: " . $json['message'] . "\n";
        }
    }
    
    // 重置POST数据
    unset($_POST);
    unset($_SERVER['REQUEST_METHOD']);
    
    echo "\n";
}

// 4. 文件权限检查
echo "4. 文件权限检查\n";
$dirs = ['config', 'runtime', 'upload', 'runtime/cache', 'runtime/temp', 'runtime/log'];

foreach ($dirs as $dir) {
    $exists = file_exists($dir);
    $writable = $exists ? is_writable($dir) : false;
    echo "  $dir: ";
    
    if (!$exists) {
        echo "❌ 不存在\n";
    } elseif (!$writable) {
        echo "⚠️ 存在但不可写\n";
    } else {
        echo "✅ 存在且可写\n";
    }
}

// 5. 安装状态检查
echo "\n5. 安装状态检查\n";
$installLock = 'install.lock';
if (file_exists($installLock)) {
    echo "  安装状态: ✅ 已安装\n";
    echo "  安装时间: " . file_get_contents($installLock) . "\n";
} else {
    echo "  安装状态: ⚠️ 未安装\n";
}

// 6. 建议和解决方案
echo "\n6. 问题诊断和建议\n";

if (!extension_loaded('pdo') || !extension_loaded('pdo_mysql')) {
    echo "  ❌ 关键问题: PDO或PDO_MySQL扩展未加载\n";
    echo "     解决方案: 在php.ini中启用extension=pdo和extension=pdo_mysql\n";
}

if (!extension_loaded('mysqli')) {
    echo "  ⚠️ 警告: mysqli扩展未加载，某些安装脚本可能无法工作\n";
    echo "     解决方案: 在php.ini中启用extension=mysqli\n";
}

// 检查是否有可用的数据库连接
$hasDbConnection = false;
foreach ($testConfigs as $config) {
    list($host, $user, $pass, $port) = $config;
    try {
        $dsn = "mysql:host=$host;port=$port";
        $pdo = new PDO($dsn, $user, $pass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 3
        ]);
        $hasDbConnection = true;
        break;
    } catch (PDOException $e) {
        // 继续尝试下一个配置
    }
}

if (!$hasDbConnection) {
    echo "  ❌ 关键问题: 无法连接到MySQL数据库\n";
    echo "     解决方案:\n";
    echo "     1. 确保MySQL服务正在运行\n";
    echo "     2. 检查MySQL用户名和密码\n";
    echo "     3. 确认MySQL端口(默认3306)是否正确\n";
    echo "     4. 检查防火墙设置\n";
} else {
    echo "  ✅ 数据库连接正常\n";
}

echo "\n=== 诊断完成 ===\n";
?>
