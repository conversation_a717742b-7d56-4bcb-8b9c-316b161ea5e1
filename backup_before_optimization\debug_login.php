<?php
/**
 * 登录调试工具
 * 模拟登录流程，捕获详细错误信息
 */

header("Content-type: text/html; charset=utf-8");
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>登录调试工具</h1>";

// 设置错误处理
set_error_handler(function($severity, $message, $file, $line) {
    echo "<div style='color: red;'>";
    echo "<strong>PHP错误:</strong> $message<br>";
    echo "<strong>文件:</strong> $file:$line<br>";
    echo "</div>";
});

try {
    // 1. 检查ThinkPHP环境
    echo "<h2>1. ThinkPHP环境检查</h2>";
    
    if (!file_exists('thinkphp/start.php')) {
        throw new Exception('ThinkPHP框架文件不存在');
    }
    echo "✓ ThinkPHP框架文件存在<br>";
    
    // 2. 手动加载ThinkPHP（简化版）
    echo "<h2>2. 手动测试登录逻辑</h2>";
    
    // 模拟登录数据
    $loginData = [
        'username' => 'admin',
        'password' => 'admin123456'
    ];
    
    // 连接数据库
    $dbConfig = include 'config/database.php';
    $pdo = new PDO(
        "mysql:host={$dbConfig['hostname']};dbname={$dbConfig['database']};charset=utf8mb4",
        $dbConfig['username'],
        $dbConfig['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // 查询用户
    $sql = "SELECT * FROM web_users WHERE u_phone = ? AND u_password = MD5(?)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$loginData['username'], $loginData['password']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "✓ 用户验证成功: {$user['u_nickname']}<br>";
        
        if ($user['u_status'] == 1) {
            echo "✓ 用户状态正常<br>";
        } else {
            echo "✗ 用户已被禁用<br>";
        }
    } else {
        echo "✗ 用户验证失败<br>";
    }
    
    // 3. 测试Session
    echo "<h2>3. Session测试</h2>";
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $_SESSION['test_uid'] = 1;
    echo "✓ Session设置成功<br>";
    
    // 4. 检查验证器
    echo "<h2>4. 验证器检查</h2>";
    if (file_exists('application/common/validate/Users.php')) {
        echo "✓ Users验证器存在<br>";
        
        // 简单验证逻辑
        $username = $loginData['username'];
        $password = $loginData['password'];
        
        if (empty($username)) {
            echo "✗ 用户名为空<br>";
        } elseif (strlen($password) < 6) {
            echo "✗ 密码长度不足<br>";
        } else {
            echo "✓ 基本验证通过<br>";
        }
    } else {
        echo "✗ Users验证器缺失<br>";
    }
    
    // 5. 测试模型加载
    echo "<h2>5. 模型文件检查</h2>";
    if (file_exists('application/common/model/Users.php')) {
        echo "✓ Users模型文件存在<br>";
    } else {
        echo "✗ Users模型文件缺失<br>";
    }
    
    // 6. 检查路由配置
    echo "<h2>6. 路由配置检查</h2>";
    $configPath = 'config/config.php';
    if (file_exists($configPath)) {
        $config = include $configPath;
        echo "默认模块: " . ($config['default_module'] ?? 'N/A') . "<br>";
        echo "默认控制器: " . ($config['default_controller'] ?? 'N/A') . "<br>";
        echo "URL转换: " . ($config['url_convert'] ? '开启' : '关闭') . "<br>";
    }
    
    // 7. 直接测试控制器方法
    echo "<h2>7. 控制器方法测试</h2>";
    
    // 模拟ThinkPHP环境
    $_POST['username'] = 'admin';
    $_POST['password'] = 'admin123456';
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    echo "POST数据设置完成<br>";
    echo "REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD'] . "<br>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>";
    echo "<strong>异常:</strong> " . $e->getMessage() . "<br>";
    echo "<strong>文件:</strong> " . $e->getFile() . ":" . $e->getLine() . "<br>";
    echo "<strong>堆栈跟踪:</strong><pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}

// 8. 查看错误日志
echo "<h2>8. 错误日志检查</h2>";
$logFiles = [
    'runtime/log',
    '/var/log/php_errors.log',
    '/tmp/php_errors.log',
    ini_get('error_log')
];

foreach ($logFiles as $logPath) {
    if ($logPath && file_exists($logPath)) {
        if (is_dir($logPath)) {
            $files = glob($logPath . '/*.log');
            if ($files) {
                echo "发现日志目录: $logPath<br>";
                foreach ($files as $file) {
                    $content = file_get_contents($file);
                    if (strpos($content, date('Y-m-d')) !== false) {
                        echo "今日日志: " . basename($file) . "<br>";
                        $lines = explode("\n", $content);
                        $recentLines = array_slice($lines, -10);
                        echo "<pre>" . implode("\n", $recentLines) . "</pre><br>";
                    }
                }
            }
        } else {
            echo "日志文件: $logPath<br>";
            if (filesize($logPath) > 0) {
                $content = tail($logPath, 10);
                echo "<pre>$content</pre><br>";
            }
        }
    }
}

// 辅助函数：读取文件末尾几行
function tail($filename, $lines = 10) {
    $handle = fopen($filename, "r");
    if (!$handle) return '';
    
    $linecounter = $lines;
    $pos = -2;
    $beginning = false;
    $text = array();
    
    while ($linecounter > 0) {
        $t = " ";
        while ($t != "\n") {
            if (fseek($handle, $pos, SEEK_END) == -1) {
                $beginning = true; 
                break; 
            }
            $t = fgetc($handle);
            $pos--;
        }
        $linecounter--;
        if ($beginning) {
            rewind($handle);
        }
        $text[$lines-$linecounter-1] = fgets($handle);
        if ($beginning) break;
    }
    fclose($handle);
    return array_reverse($text);
}

echo "<hr>";
echo "<p><strong>调试完成!</strong> 请查看上述信息以定位500错误的原因。</p>";
?>