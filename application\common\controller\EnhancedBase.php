<?php
namespace app\common\controller;
use think\Controller;
use think\Db;
use think\Session;
use think\Request;
use think\Config;
use app\common\model\OperationLogs;
use app\common\model\SystemConfig;

/**
 * 增强版基础控制器
 * 提供安全增强、日志记录、输入验证等功能
 */
abstract class EnhancedBase extends Controller{
    
    public $page = 20;
    protected $LoginStatus = false;
    protected $Url = "";
    protected $controller = "";
    protected $action = "";
    protected $userType = 1; // 1:管理员 2:分站 3:分销
    
    public $table_prefix = "web_";
    
    // 无需登录的路由白名单
    protected $UrlNoPower = [
        "index/index",
        "index/login",
        "index/outlogin",
    ];
    
    // 登录后无需权限验证的公共模块
    protected $ActionNoPower = [
        "users/logs",
        "users/password",
    ];
    
    public function _initialize(){
        $this->initSecurity();
        $this->webConfig();
        $this->SetUrl();
        $this->checkAccess();
        $this->recordOperation();
    }
    
    /**
     * 初始化安全检查
     */
    protected function initSecurity(){
        // 检查IP白名单
        $this->CheckWhiteIP();
        
        // 检查请求频率限制
        $this->checkRequestFrequency();
        
        // 检查SQL注入防护
        $this->checkSqlInjection();
    }
    
    /**
     * 检查访问权限
     */
    protected function checkAccess(){
        if(!in_array($this->Url, $this->UrlNoPower)){
            $LoginStatus = $this->CheckLoginStatus();
            switch($LoginStatus){
                case 1: 
                    $this->responseError('请先登录', 9999);
                break;
                case 2: 
                    $this->responseError('权限不通过', 9998);
                break;
                case 3:
                    define("__UID__", session("uid"));
                break;
            }
        }
    }
    
    /**
     * 记录操作日志
     */
    protected function recordOperation(){
        if(defined("__UID__") && !empty(__UID__)){
            $params = array_merge(
                Request::instance()->param(),
                Request::instance()->file()
            );
            
            // 过滤敏感信息
            $filteredParams = $this->filterSensitiveData($params);
            
            OperationLogs::record(
                $this->userType,
                __UID__,
                $this->action,
                $filteredParams
            );
        }
    }
    
    /**
     * 过滤敏感数据
     */
    protected function filterSensitiveData($data){
        $sensitiveKeys = ['password', 'oldpassword', 'endpassword', 'apikey', 'token'];
        
        foreach($sensitiveKeys as $key){
            if(isset($data[$key])){
                $data[$key] = '***';
            }
        }
        
        return $data;
    }
    
    /**
     * 请求频率限制检查
     */
    protected function checkRequestFrequency(){
        $ip = Request::instance()->ip();
        $cacheKey = "request_freq_{$ip}";
        $requests = cache($cacheKey) ?: 0;
        
        $limit = SystemConfig::getValue('request_freq_limit', 100);
        
        if($requests > $limit){
            $this->responseError('请求过于频繁，请稍后再试', 9997);
        }
        
        cache($cacheKey, $requests + 1, 60); // 1分钟窗口
    }
    
    /**
     * SQL注入检查
     */
    protected function checkSqlInjection(){
        $params = Request::instance()->param();
        $dangerousPatterns = [
            '/union\s+select/i',
            '/drop\s+table/i',
            '/delete\s+from/i',
            '/update\s+.*\s+set/i',
            '/insert\s+into/i',
            '/script\s*>/i',
            '/<\s*script/i'
        ];
        
        foreach($params as $value){
            if(is_string($value)){
                foreach($dangerousPatterns as $pattern){
                    if(preg_match($pattern, $value)){
                        OperationLogs::record($this->userType, session("uid") ?: 0, 'security_violation', [
                            'type' => 'sql_injection_attempt',
                            'value' => $value,
                            'ip' => Request::instance()->ip()
                        ]);
                        $this->responseError('检测到危险操作', 9996);
                    }
                }
            }
        }
    }
    
    /**
     * 统一错误响应
     */
    protected function responseError($message, $code = 1000){
        if(Request::instance()->isAjax()){
            echo json_encode(['status'=>$code,'msg'=>$message]);
            exit;
        }else{
            exit($message);
        }
    }
    
    /**
     * 输入验证增强
     */
    protected function validateInput($data, $rules){
        foreach($rules as $field => $rule){
            if(!isset($data[$field])){
                return ['status'=>1001,'msg'=>"{$field} 字段缺失"];
            }
            
            if($rule['required'] && empty($data[$field])){
                return ['status'=>1001,'msg'=>"{$field} 不能为空"];
            }
            
            if(isset($rule['max_length']) && mb_strlen($data[$field]) > $rule['max_length']){
                return ['status'=>1001,'msg'=>"{$field} 长度超过限制"];
            }
            
            if(isset($rule['pattern']) && !preg_match($rule['pattern'], $data[$field])){
                return ['status'=>1001,'msg'=>"{$field} 格式不正确"];
            }
        }
        
        return ['status'=>1,'msg'=>'验证通过'];
    }
    
    // 保持原有方法兼容性
    public function CheckWhiteIP(){
        if(!file_exists(CONF_PATH.'extra/ip.php')){
            return;
        }
        
        Config::load(CONF_PATH.'extra/ip.php');
        $ip = config('ip');
        if(config('onoff')){
            $doip = $_SERVER['HTTP_HOST'];
            $ip_array = explode("|",$ip);
            if(!in_array($doip,$ip_array)){
                OperationLogs::record(0, 0, 'ip_blocked', [
                    'blocked_ip' => $doip,
                    'allowed_ips' => $ip_array
                ]);
                exit("Error：系统A级错误，请与系统管理员联系！<br>系统已经记录了您的访问IP：".$doip);
            }
        }
    }
    
    protected function CheckLoginStatus(){
        if(empty(session("uid"))){
            return 1;
        }else{
            import('Auth.Auth');
            $Auth = new \Auth;
            $Auth->instance();
            if(!in_array($this->Url,$this->ActionNoPower)){
                if($Auth->check($this->Url,session("uid"))){
                    $this->ShowActionPower($Auth->AuthList_GL);
                    return 3;
                }else{
                    return 2;
                }
            }else{
                return 3;
            }
        }
    }
    
    public function ShowActionPower($AuthList){
        foreach($this->ActionPower as $key => $value){
            $controller = strtolower($this->controller."/".$key);
            if(in_array($controller,$AuthList)){
                $this->ActionPower[$key] = true;
            }else{
                $this->ActionPower[$key] = false;
            }
        }
        $this->assign("__APS__",$this->ActionPower);
    }
    
    protected function webConfig(){
        if(file_exists(CONF_PATH.'extra/web.php')){
            Config::load(CONF_PATH.'extra/web.php');
            $web = config('web');
            $this->assign("subweb",$web);
        }
        
        $this->page = SystemConfig::getValue('default_page_size', 15);
    }
    
    protected function SetUrl(){
        $request = Request::instance();
        $this->controller = $request->controller();
        $this->action = $request->action();
        $this->Url = strtolower($this->controller."/".$this->action);
    }
}