<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>分销管理系统 - 安装向导 (调试版)</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .installer { width: 100%; max-width: 600px; background: white; border-radius: 12px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 30px; text-align: center; }
        .header h1 { font-size: 28px; margin-bottom: 8px; }
        .header p { opacity: 0.9; font-size: 16px; }
        .content { padding: 40px; }
        .step { margin-bottom: 30px; }
        .step h3 { color: #333; margin-bottom: 20px; font-size: 20px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #555; }
        .form-group input { width: 100%; padding: 12px 16px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 16px; transition: border-color 0.3s; }
        .form-group input:focus { outline: none; border-color: #667eea; }
        .btn { width: 100%; padding: 14px; background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: transform 0.2s; }
        .btn:hover { transform: translateY(-2px); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .alert { padding: 16px; border-radius: 8px; margin-bottom: 20px; }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .debug-info { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 15px 0; border-radius: 8px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
        .loading { display: none; text-align: center; padding: 20px; }
        .loading::after { content: ''; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite; display: inline-block; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .step.hidden { display: none; }
        .success-info { background: #f8f9fa; border-radius: 8px; padding: 20px; }
        .success-info h4 { color: #28a745; margin-bottom: 10px; }
        .success-info p { margin-bottom: 8px; }
    </style>
</head>
<body>
    <div class="installer">
        <div class="header">
            <h1>分销管理系统 (调试版)</h1>
            <p>详细的安装调试信息</p>
        </div>
        
        <div class="content">
            <!-- 数据库配置步骤 -->
            <div id="step-database" class="step">
                <h3>数据库配置</h3>
                <div id="error-message"></div>
                
                <form id="db-form">
                    <div class="form-group">
                        <label>数据库主机:</label>
                        <input type="text" id="hostname" value="localhost" required>
                    </div>
                    <div class="form-group">
                        <label>数据库端口:</label>
                        <input type="text" id="hostport" value="3306" required>
                    </div>
                    <div class="form-group">
                        <label>数据库名:</label>
                        <input type="text" id="database" placeholder="请输入数据库名" required>
                    </div>
                    <div class="form-group">
                        <label>用户名:</label>
                        <input type="text" id="username" placeholder="请输入数据库用户名" required>
                    </div>
                    <div class="form-group">
                        <label>密码:</label>
                        <input type="password" id="password" placeholder="请输入数据库密码">
                    </div>
                    <button type="button" class="btn" onclick="installSystem()">开始安装</button>
                </form>
                
                <div id="debug-info" class="debug-info" style="display: none;">
                    <strong>调试信息:</strong><br>
                    <div id="debug-content"></div>
                </div>
            </div>
            
            <!-- 加载中 -->
            <div id="loading" class="loading"></div>
            
            <!-- 安装完成 -->
            <div id="step-complete" class="step hidden">
                <h3>安装完成</h3>
                <div class="success-info">
                    <h4>🎉 系统安装成功！</h4>
                    <p>管理员账号：admin</p>
                    <p>管理员密码：admin123456</p>
                    <button class="btn" onclick="window.location.href='index.php'">进入系统</button>
                </div>
            </div>
        </div>
    </div>

    <script>
    function installSystem(){
        const form = document.getElementById('db-form');
        const loading = document.getElementById('loading');
        const errorDiv = document.getElementById('error-message');
        const debugInfo = document.getElementById('debug-info');
        const debugContent = document.getElementById('debug-content');
        
        // 显示调试信息
        debugInfo.style.display = 'block';
        debugContent.innerHTML = '开始安装过程...<br>';
        
        // 验证表单
        if(!form.checkValidity()){
            showError('请填写所有必填字段');
            return;
        }
        
        // 显示加载状态
        document.getElementById('step-database').style.display = 'none';
        loading.style.display = 'block';
        errorDiv.innerHTML = '';
        
        const config = {
            step: 'install',
            hostname: document.getElementById('hostname').value,
            hostport: document.getElementById('hostport').value,
            database: document.getElementById('database').value,
            username: document.getElementById('username').value,
            password: document.getElementById('password').value
        };
        
        debugContent.innerHTML += '请求配置: ' + JSON.stringify(config) + '<br>';
        debugContent.innerHTML += '发送POST请求到 install_api.php...<br>';
        
        fetch('install_api.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: Object.keys(config).map(key => key + '=' + encodeURIComponent(config[key])).join('&')
        })
        .then(response => {
            debugContent.innerHTML += '收到HTTP响应，状态码: ' + response.status + '<br>';
            debugContent.innerHTML += '响应头 Content-Type: ' + (response.headers.get('Content-Type') || 'unknown') + '<br>';
            
            if(!response.ok){
                debugContent.innerHTML += '响应状态不正常: ' + response.status + ' ' + response.statusText + '<br>';
                throw new Error('HTTP ' + response.status + ': ' + response.statusText);
            }
            
            // 先获取原始文本，然后尝试解析JSON
            return response.text();
        })
        .then(responseText => {
            debugContent.innerHTML += '原始响应内容 (' + responseText.length + ' 字符):<br>';
            debugContent.innerHTML += '<textarea style="width:100%;height:100px;font-size:11px;">' + responseText + '</textarea><br>';
            
            // 检查响应内容
            const trimmedResponse = responseText.trim();
            if (!trimmedResponse) {
                throw new Error('服务器返回空响应');
            }
            
            if (trimmedResponse.charAt(0) !== '{' && trimmedResponse.charAt(0) !== '[') {
                debugContent.innerHTML += '❌ 响应不是JSON格式，首字符: "' + trimmedResponse.charAt(0) + '"<br>';
                if (trimmedResponse.includes('<html') || trimmedResponse.includes('<!DOCTYPE')) {
                    debugContent.innerHTML += '❌ 响应是HTML页面，可能是PHP错误页面<br>';
                }
                throw new Error('服务器返回的不是JSON格式: ' + trimmedResponse.substring(0, 100));
            }
            
            // 尝试解析JSON
            try {
                const data = JSON.parse(responseText);
                debugContent.innerHTML += '✅ JSON解析成功<br>';
                debugContent.innerHTML += 'JSON数据: ' + JSON.stringify(data, null, 2) + '<br>';
                return data;
            } catch (jsonError) {
                debugContent.innerHTML += '❌ JSON解析失败: ' + jsonError.message + '<br>';
                throw new Error('JSON解析错误: ' + jsonError.message);
            }
        })
        .then(data => {
            loading.style.display = 'none';
            document.getElementById('step-database').style.display = 'block';
            
            debugContent.innerHTML += '处理响应数据...<br>';
            
            if(data.status){
                debugContent.innerHTML += '✅ 安装成功<br>';
                document.getElementById('step-complete').className = 'step';
                document.getElementById('step-database').style.display = 'none';
            } else {
                debugContent.innerHTML += '❌ 安装失败: ' + data.message + '<br>';
                showError(data.message || '安装失败');
            }
        })
        .catch(error => {
            loading.style.display = 'none';
            document.getElementById('step-database').style.display = 'block';
            
            debugContent.innerHTML += '❌ 捕获到错误: ' + error.message + '<br>';
            debugContent.innerHTML += '错误堆栈: ' + (error.stack || 'N/A') + '<br>';
            
            showError('安装过程中发生错误: ' + error.message);
        });
    }
    
    function showError(message){
        document.getElementById('error-message').innerHTML = 
            `<div class="alert alert-danger">${message}</div>`;
    }
    </script>
</body>
</html>