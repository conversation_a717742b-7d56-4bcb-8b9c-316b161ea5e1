<?php
namespace app\common\service;
use think\Db;
use app\common\model\SystemConfig;

/**
 * 系统监控服务
 * 提供系统健康检查、性能监控等功能
 */
class SystemMonitor {
    
    /**
     * 执行系统健康检查
     */
    public static function healthCheck(){
        $results = [];
        
        // 数据库连接检查
        $results['database'] = self::checkDatabase();
        
        // 文件权限检查
        $results['permissions'] = self::checkPermissions();
        
        // 磁盘空间检查
        $results['disk_space'] = self::checkDiskSpace();
        
        // 缓存状态检查
        $results['cache'] = self::checkCache();
        
        // 核心表结构检查
        $results['tables'] = self::checkTables();
        
        return $results;
    }
    
    /**
     * 数据库连接检查
     */
    protected static function checkDatabase(){
        try {
            Db::execute("SELECT 1");
            return ['status' => 'ok', 'message' => '数据库连接正常'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => '数据库连接失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 文件权限检查
     */
    protected static function checkPermissions(){
        $checkPaths = [
            'runtime/' => '运行时目录',
            'upload/' => '上传目录',
            'config/' => '配置目录'
        ];
        
        $results = [];
        
        foreach($checkPaths as $path => $desc){
            if(!file_exists($path)){
                $results[$path] = ['status' => 'warning', 'message' => "{$desc}不存在"];
                continue;
            }
            
            if(!is_writable($path)){
                $results[$path] = ['status' => 'error', 'message' => "{$desc}不可写"];
            } else {
                $results[$path] = ['status' => 'ok', 'message' => "{$desc}权限正常"];
            }
        }
        
        return $results;
    }
    
    /**
     * 磁盘空间检查
     */
    protected static function checkDiskSpace(){
        $freeBytes = disk_free_space(".");
        $totalBytes = disk_total_space(".");
        
        if($freeBytes === false || $totalBytes === false){
            return ['status' => 'error', 'message' => '无法获取磁盘信息'];
        }
        
        $freeGB = round($freeBytes / 1024 / 1024 / 1024, 2);
        $totalGB = round($totalBytes / 1024 / 1024 / 1024, 2);
        $usedPercent = round(($totalBytes - $freeBytes) / $totalBytes * 100, 2);
        
        $status = $usedPercent > 90 ? 'error' : ($usedPercent > 80 ? 'warning' : 'ok');
        
        return [
            'status' => $status,
            'message' => "磁盘使用率: {$usedPercent}%, 可用: {$freeGB}GB/{$totalGB}GB"
        ];
    }
    
    /**
     * 缓存状态检查
     */
    protected static function checkCache(){
        try {
            cache('health_check_test', 'test_value', 60);
            $testValue = cache('health_check_test');
            
            if($testValue === 'test_value'){
                cache('health_check_test', null);
                return ['status' => 'ok', 'message' => '缓存功能正常'];
            } else {
                return ['status' => 'error', 'message' => '缓存读写异常'];
            }
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => '缓存异常: ' . $e->getMessage()];
        }
    }
    
    /**
     * 核心表结构检查
     */
    protected static function checkTables(){
        $requiredTables = [
            'web_users' => '用户表',
            'web_substation' => '分站表',
            'web_distribution' => '分销表',
            'web_bill' => '账单表',
            'web_auth_rule' => '权限规则表',
            'web_auth_group' => '权限分组表'
        ];
        
        $results = [];
        
        foreach($requiredTables as $table => $desc){
            try {
                $count = Db::name(str_replace('web_', '', $table))->count();
                $results[$table] = [
                    'status' => 'ok', 
                    'message' => "{$desc}正常 (数据量: {$count})"
                ];
            } catch (\Exception $e) {
                $results[$table] = [
                    'status' => 'error', 
                    'message' => "{$desc}异常: " . $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * 获取系统状态概览
     */
    public static function getSystemOverview(){
        // 用户统计
        $userStats = [
            'total' => Db::name('users')->count(),
            'active' => Db::name('users')->where('u_status', 1)->count(),
            'today_logins' => Db::name('users_logs')
                ->where('ul_addtime', '>=', date('Y-m-d'))
                ->where('ul_type', 1)
                ->count()
        ];
        
        // 分站统计
        $substationStats = [
            'total' => Db::name('substation')->count(),
            'active' => Db::name('substation')->where('su_status', 1)->count(),
            'expired' => Db::name('substation')->where('su_endtime', '<', date('Y-m-d'))->count()
        ];
        
        // 分销统计
        $distributionStats = [
            'total' => Db::name('distribution')->count(),
            'active' => Db::name('distribution')->where('du_status', 1)->count()
        ];
        
        // 账单统计
        $billStats = [
            'today_count' => Db::name('bill')
                ->where('bl_addtime', '>=', date('Y-m-d'))
                ->count(),
            'today_amount' => Db::name('bill')
                ->where('bl_addtime', '>=', date('Y-m-d'))
                ->where('bl_status', 1)
                ->sum('bl_money') ?: 0
        ];
        
        return [
            'users' => $userStats,
            'substation' => $substationStats,
            'distribution' => $distributionStats,
            'bills' => $billStats,
            'check_time' => date('Y-m-d H:i:s')
        ];
    }
}