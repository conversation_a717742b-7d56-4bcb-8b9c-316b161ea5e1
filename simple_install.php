<?php
/**
 * 简化版系统安装脚本
 * 解决 PDO 缓冲查询问题
 */

// 立即关闭错误显示，防止污染JSON输出
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

define('INSTALL_PATH', __DIR__);
define('INSTALL_LOCK', INSTALL_PATH . '/install.lock');

// 处理安装请求
if($_SERVER['REQUEST_METHOD'] === 'POST'){
    // 立即开始输出缓冲以确保JSON响应干净
    ob_start();
    
    // 检查是否已安装（POST请求时返回JSON格式）
    if(file_exists(INSTALL_LOCK)){
        ob_clean(); // 清理任何已有输出
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'status' => false, 
            'message' => '系统已经安装，如需重新安装请删除 install.lock 文件'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $step = $_POST['step'] ?? '';
    
    if($step === 'install'){
        try {
            $config = [
                'hostname' => $_POST['hostname'] ?? 'localhost',
                'database' => $_POST['database'] ?? '',
                'username' => $_POST['username'] ?? '',
                'password' => $_POST['password'] ?? '',
                'hostport' => $_POST['hostport'] ?? '3306'
            ];
            
            $result = installDatabase($config);
            
            // 清理输出缓冲，确保没有意外输出
            ob_clean();
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            exit;
        } catch (Exception $e) {
            // 清理输出缓冲
            ob_clean();
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode([
                'status' => false, 
                'message' => '安装过程发生异常: ' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    } else {
        // 未知的步骤
        ob_clean();
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'status' => false, 
            'message' => '无效的安装步骤'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 只有在非POST请求时才设置HTML内容类型
header("Content-type: text/html; charset=utf-8");

// 检查是否已安装（GET请求时显示HTML提示）
if(file_exists(INSTALL_LOCK)){
    echo '<!DOCTYPE html>
<html><head><meta charset="utf-8"><title>系统已安装</title></head>
<body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
    <h2>系统已经安装</h2>
    <p>如需重新安装请删除 <code>install.lock</code> 文件</p>
    <p><a href="index.php">进入系统</a></p>
</body></html>';
    exit;
}

function installDatabase($config){
    try {
        // 强制关闭所有可能的输出
        ob_start();
        error_reporting(0);
        ini_set('display_errors', 0);
        ini_set('log_errors', 1);
        
        // 验证配置参数
        if(empty($config['hostname']) || empty($config['database']) || empty($config['username'])) {
            throw new Exception('数据库配置信息不完整');
        }
        
        // 使用 mysqli 替代 PDO，更好地处理多语句
        $link = @mysqli_connect(
            $config['hostname'],
            $config['username'], 
            $config['password'],
            '',
            intval($config['hostport'])
        );
        
        if(!$link){
            throw new Exception('数据库连接失败: ' . mysqli_connect_error());
        }
        
        if(!mysqli_set_charset($link, 'utf8mb4')) {
            throw new Exception('设置字符编码失败');
        }
        
        // 创建数据库
        $sql = "CREATE DATABASE IF NOT EXISTS `{$config['database']}` DEFAULT CHARSET utf8mb4";
        if(!mysqli_query($link, $sql)){
            throw new Exception('创建数据库失败: ' . mysqli_error($link));
        }
        
        // 选择数据库
        if(!mysqli_select_db($link, $config['database'])){
            throw new Exception('选择数据库失败: ' . mysqli_error($link));
        }
        
        // 执行核心表创建
        $coreStructures = [
            // 用户表
            "CREATE TABLE IF NOT EXISTS `web_users` (
              `u_id` int(11) NOT NULL AUTO_INCREMENT,
              `u_phone` varchar(20) NOT NULL,
              `u_password` varchar(64) NOT NULL,
              `u_nickname` varchar(50) DEFAULT '',
              `u_status` tinyint(1) DEFAULT 1,
              `u_supermanage` tinyint(1) DEFAULT 1,
              `u_count` int(11) DEFAULT 0,
              `u_regtime` datetime DEFAULT NULL,
              `u_this_time` datetime DEFAULT NULL,
              `u_this_ip` varchar(50) DEFAULT '',
              `u_last_time` datetime DEFAULT NULL,
              `u_last_ip` varchar(50) DEFAULT '',
              PRIMARY KEY (`u_id`),
              UNIQUE KEY `uk_phone` (`u_phone`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
            
            // 用户日志表
            "CREATE TABLE IF NOT EXISTS `web_users_logs` (
              `ul_id` int(11) NOT NULL AUTO_INCREMENT,
              `u_id` int(11) NOT NULL,
              `ul_type` tinyint(1) DEFAULT 1,
              `ul_addtime` datetime NOT NULL,
              `ul_ip` varchar(50) DEFAULT '',
              PRIMARY KEY (`ul_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
            
            // 权限规则表
            "CREATE TABLE IF NOT EXISTS `web_auth_rule` (
              `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
              `name` char(80) NOT NULL DEFAULT '',
              `title` char(20) NOT NULL DEFAULT '',
              `type` tinyint(1) NOT NULL DEFAULT 1,
              `status` tinyint(1) NOT NULL DEFAULT 1,
              `condition` char(100) NOT NULL DEFAULT '',
              `said` int(11) DEFAULT 0,
              `sort` int(11) DEFAULT 0,
              PRIMARY KEY (`id`),
              UNIQUE KEY `name` (`name`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
            
            // 权限分组表
            "CREATE TABLE IF NOT EXISTS `web_auth_group` (
              `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
              `title` char(100) NOT NULL DEFAULT '',
              `status` tinyint(1) NOT NULL DEFAULT 1,
              `rules` char(80) NOT NULL DEFAULT '',
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
            
            // 语音列表表
            "CREATE TABLE IF NOT EXISTS `web_voicelist` (
              `vol_id` int(11) NOT NULL AUTO_INCREMENT,
              `vol_title` varchar(100) NOT NULL,
              `vol_content` text,
              `vol_file_path` varchar(255) DEFAULT '',
              `vol_status` tinyint(1) DEFAULT 1,
              `vol_addtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `vol_updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`vol_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
            
            // 系统配置表
            "CREATE TABLE IF NOT EXISTS `web_system_config` (
              `config_id` int(11) NOT NULL AUTO_INCREMENT,
              `config_key` varchar(50) NOT NULL,
              `config_value` text,
              `config_type` varchar(20) DEFAULT 'string',
              `config_group` varchar(30) DEFAULT 'system',
              `config_desc` varchar(100) DEFAULT '',
              `is_readonly` tinyint(1) DEFAULT 0,
              `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`config_id`),
              UNIQUE KEY `uk_key` (`config_key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
        ];
        
        // 执行完整表结构创建
        $sqlFile = 'database_complete_init.sql';
        if (file_exists($sqlFile)) {
            $executedCount = executeSqlFile($link, $sqlFile);
            if ($executedCount === false) {
                throw new Exception('SQL文件执行失败，请检查日志');
            }
        } else {
            // 如果完整SQL文件不存在，执行基础表创建
            foreach($coreStructures as $sql){
                if(!mysqli_query($link, $sql)){
                    $error = mysqli_error($link);
                    if(!preg_match('/(already exists|duplicate)/i', $error)){
                        throw new Exception('创建表失败: ' . $error);
                    }
                }
            }
        }
        
        // 插入初始数据
        $initData = [
            // 创建管理员账号
            "INSERT IGNORE INTO `web_users` (`u_phone`, `u_password`, `u_nickname`, `u_status`, `u_supermanage`, `u_regtime`) VALUES ('admin', MD5('admin123456'), '系统管理员', 1, 2, NOW())",
            
            // 基础权限规则
            "INSERT IGNORE INTO `web_auth_rule` (`name`, `title`, `type`, `status`, `said`, `sort`) VALUES 
             ('index', '首页管理', 1, 1, 0, 1),
             ('users', '用户管理', 1, 1, 0, 2),
             ('substation', '分站管理', 1, 1, 0, 3)",
            
            // 管理员权限组
            "INSERT IGNORE INTO `web_auth_group` (`title`, `status`, `rules`) VALUES ('超级管理员', 1, '1,2,3')",
            
            // 基础系统配置
            "INSERT IGNORE INTO `web_system_config` (`config_key`, `config_value`, `config_desc`) VALUES 
             ('site_name', '分销管理系统', '网站名称'),
             ('default_page_size', '15', '默认分页大小')"
        ];
        
        foreach($initData as $sql){
            if(!mysqli_query($link, $sql)){
                $error = mysqli_error($link);
                if(!preg_match('/(duplicate|exists)/i', $error)){
                    throw new Exception('插入初始数据失败: ' . $error);
                }
            }
        }
        
        mysqli_close($link);
        
        // 生成配置文件
        generateDbConfig($config);
        
        // 自动配置IP白名单
        generateIpConfig();
        
        // 创建必要目录
        createRequiredDirectories();
        
        // 创建安装锁
        file_put_contents(INSTALL_LOCK, "安装时间: " . date('Y-m-d H:i:s'));
        
        // 清理输出缓冲
        ob_end_clean();
        
        return ['status' => true, 'message' => '数据库初始化成功'];
        
    } catch (Exception $e) {
        // 清理输出缓冲
        ob_end_clean();
        return ['status' => false, 'message' => $e->getMessage()];
    }
}

function generateDbConfig($config){
    $configContent = "<?php\n// 数据库配置文件\nreturn [\n";
    $configContent .= "    'type'            => 'mysql',\n";
    $configContent .= "    'hostname'        => '{$config['hostname']}',\n";
    $configContent .= "    'database'        => '{$config['database']}',\n";
    $configContent .= "    'username'        => '{$config['username']}',\n";
    $configContent .= "    'password'        => '{$config['password']}',\n";
    $configContent .= "    'hostport'        => '{$config['hostport']}',\n";
    $configContent .= "    'dsn'             => '',\n";
    $configContent .= "    'params'          => [],\n";
    $configContent .= "    'charset'         => 'utf8mb4',\n";
    $configContent .= "    'prefix'          => 'web_',\n";
    $configContent .= "    'debug'           => true,\n";
    $configContent .= "    'deploy'          => 0,\n";
    $configContent .= "    'rw_separate'     => false,\n";
    $configContent .= "    'master_num'      => 1,\n";
    $configContent .= "    'slave_no'        => '',\n";
    $configContent .= "    'read_master'     => false,\n";
    $configContent .= "    'fields_strict'   => true,\n";
    $configContent .= "    'resultset_type'  => 'array',\n";
    $configContent .= "    'auto_timestamp'  => false,\n";
    $configContent .= "    'datetime_format' => 'Y-m-d H:i:s',\n";
    $configContent .= "    'sql_explain'     => false,\n";
    $configContent .= "];\n";
    
    return file_put_contents('config/database.php', $configContent);
}

function generateIpConfig(){
    // 自动获取当前访问的域名/IP
    $currentHost = $_SERVER['HTTP_HOST'] ?? 'localhost';
    
    // 生成IP白名单配置，默认关闭白名单功能
    $ipConfigContent = "<?php\n// IP白名单配置\nreturn [\n";
    $ipConfigContent .= "    'onoff' => false, // 默认关闭IP白名单，避免安装后无法访问\n";
    $ipConfigContent .= "    'ip' => \"{$currentHost}|localhost|127.0.0.1\",\n";
    $ipConfigContent .= "];\n";
    
    // 确保目录存在
    if(!file_exists('config/extra')){
        mkdir('config/extra', 0755, true);
    }
    
    return file_put_contents('config/extra/ip.php', $ipConfigContent);
}

function createRequiredDirectories(){
    $dirs = ['runtime', 'runtime/cache', 'runtime/temp', 'runtime/log', 'upload', 'config/extra'];
    
    foreach($dirs as $dir){
        if(!file_exists($dir)){
            mkdir($dir, 0755, true);
        }
    }
    
    return true;
}

/**
 * 改进的SQL文件执行函数
 * 正确处理多行语句和引号问题
 */
function executeSqlFile($link, $sqlFile) {
    try {
        $sql = file_get_contents($sqlFile);
        $statements = parseSQL($sql);
        $executedCount = 0;
        $errors = [];
        
        foreach ($statements as $index => $statement) {
            $statement = trim($statement);
            
            // 跳过空语句、SET语句和注释
            if (empty($statement) || 
                strpos($statement, 'SET') === 0 || 
                strpos($statement, '--') === 0 ||
                strpos($statement, '#') === 0) {
                continue;
            }
            
            // 执行语句
            if (!mysqli_query($link, $statement)) {
                $error = mysqli_error($link);
                
                // 忽略已存在的错误
                if (!preg_match('/(already exists|duplicate|table.*exists)/i', $error)) {
                    $errors[] = "语句 " . ($index + 1) . ": " . $error . "\nSQL: " . substr($statement, 0, 100) . "...";
                    
                    // 记录到日志文件
                    error_log("SQL执行错误 [语句 " . ($index + 1) . "]: $error", 3, 'install_errors.log');
                    error_log("问题SQL: $statement", 3, 'install_errors.log');
                } else {
                    // 表已存在，这是正常的
                    $executedCount++;
                }
            } else {
                $executedCount++;
            }
        }
        
        // 如果有严重错误，抛出异常
        if (!empty($errors) && count($errors) > 5) { // 允许少量错误
            throw new Exception("SQL执行错误过多：\n" . implode("\n", array_slice($errors, 0, 3)) . "\n...");
        }
        
        return $executedCount;
        
    } catch (Exception $e) {
        error_log("SQL文件执行异常: " . $e->getMessage(), 3, 'install_errors.log');
        return false;
    }
}

/**
 * 改进的SQL解析函数
 * 正确处理引号和分号
 */
function parseSQL($sql) {
    $statements = [];
    $current = '';
    $inQuotes = false;
    $quoteChar = '';
    $length = strlen($sql);
    
    for ($i = 0; $i < $length; $i++) {
        $char = $sql[$i];
        
        // 处理转义符
        if ($char === '\\' && $inQuotes) {
            $current .= $char;
            if ($i + 1 < $length) {
                $i++; // 跳过下一个字符
                $current .= $sql[$i];
            }
            continue;
        }
        
        // 处理引号
        if (($char === '"' || $char === "'" || $char === '`') && !$inQuotes) {
            $inQuotes = true;
            $quoteChar = $char;
        } elseif ($char === $quoteChar && $inQuotes) {
            $inQuotes = false;
            $quoteChar = '';
        }
        
        // 处理注释（只在非引号内）
        if (!$inQuotes && $char === '-' && $i + 1 < $length && $sql[$i + 1] === '-') {
            // 跳到行末
            while ($i < $length && $sql[$i] !== "\n") {
                $current .= $sql[$i];
                $i++;
            }
            continue;
        }
        
        // 分号分割（但不在引号内）
        if ($char === ';' && !$inQuotes) {
            $statement = trim($current);
            if (!empty($statement)) {
                $statements[] = $statement;
            }
            $current = '';
            continue;
        }
        
        $current .= $char;
    }
    
    // 添加最后一个语句（如果有）
    $statement = trim($current);
    if (!empty($statement)) {
        $statements[] = $statement;
    }
    
    return $statements;
}