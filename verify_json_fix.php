<?php
/**
 * 验证JSON修复是否生效
 */

header("Content-type: text/html; charset=utf-8");

echo "<h1>JSON修复验证</h1>";

// 检查install_api.php
echo "<h2>1. 检查 install_api.php</h2>";

$installApiContent = file_get_contents('install_api.php');
$trimmedContent = rtrim($installApiContent);

if (substr($trimmedContent, -2) === '?>') {
    echo "<div style='color: red; padding: 10px; border: 1px solid red;'>❌ install_api.php 仍有PHP结束标签</div>";
} else {
    echo "<div style='color: green; padding: 10px; border: 1px solid green;'>✅ install_api.php 已移除PHP结束标签</div>";
}

// 测试install_api.php的实际响应
echo "<h2>2. 测试 install_api.php 响应</h2>";

$testData = [
    'step' => 'install',
    'hostname' => 'invalid_host', // 故意使用无效主机，应该返回JSON错误
    'database' => 'test_db',
    'username' => 'test_user',
    'password' => 'test_pass'
];

$postData = http_build_query($testData);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/x-www-form-urlencoded\r\n",
        'content' => $postData
    ]
]);

$url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/install_api.php';
$response = @file_get_contents($url, false, $context);

if ($response === false) {
    echo "<div style='color: red;'>❌ 无法连接到 install_api.php</div>";
} else {
    echo "<div style='background: #f5f5f5; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
    echo "<strong>响应长度:</strong> " . strlen($response) . " 字符<br>";
    echo "<strong>响应内容:</strong><br>";
    echo "<textarea style='width: 100%; height: 100px; font-family: monospace;'>" . htmlspecialchars($response) . "</textarea>";
    
    $jsonData = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<div style='color: green; margin-top: 10px;'>";
        echo "✅ <strong>JSON解析成功!</strong><br>";
        echo "状态: " . ($jsonData['status'] ? '成功' : '失败') . "<br>";
        echo "消息: " . htmlspecialchars($jsonData['message']) . "<br>";
        echo "</div>";
    } else {
        echo "<div style='color: red; margin-top: 10px;'>";
        echo "❌ <strong>JSON解析失败:</strong> " . json_last_error_msg() . "<br>";
        
        // 详细分析
        $firstChar = substr(trim($response), 0, 1);
        echo "首字符: '$firstChar'<br>";
        
        if (strpos($response, '<') !== false) {
            echo "⚠️ 响应包含HTML标签<br>";
        }
        if (preg_match('/\r\n\r\n/', $response)) {
            echo "⚠️ 响应可能包含HTTP头信息<br>";
        }
        echo "</div>";
    }
    echo "</div>";
}

// 测试浏览器兼容性
echo "<h2>3. JavaScript fetch测试</h2>";
echo "<div id='fetch-test-result' style='background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0;'>点击按钮测试...</div>";
echo "<button onclick='testFetch()' style='background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;'>测试 Fetch JSON</button>";

echo "<script>
function testFetch() {
    const resultDiv = document.getElementById('fetch-test-result');
    resultDiv.innerHTML = '正在测试...';
    
    const testData = {
        step: 'install',
        hostname: 'localhost',
        database: 'test_db_' + Date.now(),
        username: 'root',
        password: ''
    };
    
    const body = Object.keys(testData).map(key => key + '=' + encodeURIComponent(testData[key])).join('&');
    
    fetch('install_api.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: body
    })
    .then(response => {
        resultDiv.innerHTML += 'HTTP状态: ' + response.status + '<br>';
        resultDiv.innerHTML += 'Content-Type: ' + (response.headers.get('Content-Type') || 'unknown') + '<br>';
        
        return response.text();
    })
    .then(responseText => {
        resultDiv.innerHTML += '原始响应: ' + responseText.substring(0, 200) + '<br>';
        
        try {
            const data = JSON.parse(responseText);
            resultDiv.innerHTML += '<span style=\"color: green;\">✅ JSON解析成功!</span><br>';
            resultDiv.innerHTML += '状态: ' + (data.status ? '成功' : '失败') + '<br>';
            resultDiv.innerHTML += '消息: ' + data.message + '<br>';
        } catch (e) {
            resultDiv.innerHTML += '<span style=\"color: red;\">❌ JSON解析失败: ' + e.message + '</span><br>';
            resultDiv.innerHTML += '首字符: \"' + responseText.trim().charAt(0) + '\"<br>';
        }
    })
    .catch(error => {
        resultDiv.innerHTML += '<span style=\"color: red;\">❌ Fetch失败: ' + error.message + '</span>';
    });
}
</script>";

echo "<h2>4. 总结</h2>";
echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 4px;'>";
echo "<strong>问题根源:</strong> PHP文件末尾的 ?> 标签后可能有空白字符，导致JSON响应被污染<br><br>";
echo "<strong>修复方案:</strong> 移除不必要的 PHP 结束标签 ?>（这是最佳实践）<br><br>";
echo "<strong>验证方法:</strong><br>";
echo "1. 如果上述测试显示JSON解析成功，说明问题已解决<br>";
echo "2. 现在可以正常使用 setup.html 进行安装<br>";
echo "3. 也可以使用 setup_debug.html 查看详细的安装过程";
echo "</div>";

echo "<p style='margin: 20px 0;'>";
echo "<a href='setup.html' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>测试 setup.html</a>";
echo "<a href='setup_debug.html' target='_blank' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>使用调试版安装</a>";
echo "</p>";
?>