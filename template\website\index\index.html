
<!DOCTYPE html>
<html style="
    background: linear-gradient(120grad, #02ff7f, #98aed5);
">
<head>
  <meta charset="utf-8">
  <title>{$subweb['title']}</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/template/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/template/layuiadmin/style/admin.css" media="all">
  <link rel="stylesheet" href="/template/layuiadmin/style/login.css" media="all">
</head>
<body>
  <div class="layadmin-user-login layadmin-user-display-show" id="LAY-user-login" style="display: none;">
    <div class="layadmin-user-login-main">
      <div class="layadmin-user-login-box layadmin-user-login-header">
        <h2>{$subweb['oaname']}</h2>
      </div>
      <div class="layadmin-user-login-box layadmin-user-login-body layui-form">
        <div class="layui-form-item">
          <label class="layadmin-user-login-icon layui-icon layui-icon-username" for="LAY-user-login-username"></label>
          <input type="text" name="username" id="username" lay-verify="required" placeholder="手机号" class="layui-input">
        </div>
        <div class="layui-form-item">
          <label class="layadmin-user-login-icon layui-icon layui-icon-password" for="LAY-user-login-password"></label>
          <input type="password" name="password" id="password" lay-verify="required" placeholder="密码" class="layui-input">
        </div>
        
        
        <div class="layui-form-item">
          <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="LAY-user-login-submit">登 入</button>
        </div>
        
      </div>
    </div>
	
	
	
	
	


    <div class="layui-trans layadmin-user-login-footer">
		<p><p>{$subweb['copyright']}</p></p>
		<p>sun website brace system 2024</p>
	</div>
  </div>
<script src="/template/layuiadmin/layui/layui.js"></script>   
<script>
layui.config({
	base: '/template/layuiadmin/' //静态资源所在路径
}).extend({
	index: 'lib/index' //主入口模块
}).use(['index', 'user'], function(){
	var $ = layui.$
    ,setter = layui.setter
    ,admin = layui.admin
    ,form = layui.form
    ,router = layui.router()
    ,search = router.search;
    form.render();
    //提交
    form.on('submit(LAY-user-login-submit)', function(obj){
		var username = $("#username").val();
		var password = $("#password").val();
		
		// 首先尝试标准ThinkPHP路由
		$.ajax({
			type:"POST",
			url:"/website/index/Login",
			dataType:"json",
			data:{
				username:username,
				password:password,
			},
			beforeSend:function(){
				layer.load(1);
			},
			success:function(res){
				layer.closeAll('loading');
				console.log('ThinkPHP Login response:', res);
				if(res && res.status == 1){
					layer.msg(res.msg || '登录成功', {offset: '15px',icon: 1,time: 1000}, function(){
						location.href = '/website/center/index.html';
					});
				}else{
					layer.msg(res.msg || '登录失败', {offset: '15px',icon: 2});
				}
			},
			error:function(jqXHR, textStatus, errorThrown){
				layer.closeAll('loading');
				console.log("ThinkPHP登录失败，尝试备用方案");
				console.log("状态:", jqXHR.status, "错误:", textStatus);
				
				// 如果ThinkPHP登录失败，尝试备用登录方案
				if(jqXHR.status == 500 || jqXHR.status == 404){
					tryAlternativeLogin(username, password);
				} else {
					layer.msg('登录请求失败: ' + jqXHR.status, {offset: '15px',icon: 2});
				}
			},
		});
		
		// 备用登录函数
		function tryAlternativeLogin(username, password){
			layer.load(1);
			$.ajax({
				type:"POST", 
				url:"/simple_login.php",
				dataType:"json",
				data:{
					username:username,
					password:password,
				},
				success:function(res){
					layer.closeAll('loading');
					console.log('备用登录响应:', res);
					if(res && res.status == 1){
						layer.msg('登录成功', {offset: '15px',icon: 1,time: 1000}, function(){
							location.href = '/website/center/index.html';
						});
					}else{
						layer.msg(res.msg || '登录失败', {offset: '15px',icon: 2});
					}
				},
				error:function(jqXHR, textStatus, errorThrown){
					layer.closeAll('loading');
					console.log("备用登录也失败:", textStatus, errorThrown);
					layer.msg('所有登录方案都失败，请联系管理员', {offset: '15px',icon: 2});
				}
			});
		}
      
    });    
  });
  </script>
</body>
</html>