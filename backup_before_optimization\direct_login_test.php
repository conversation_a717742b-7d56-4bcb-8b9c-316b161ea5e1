<?php
/**
 * 直接登录测试 - 绕过ThinkPHP框架
 */

header("Content-type: application/json; charset=utf-8");
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // 连接数据库
    $dbConfig = include 'config/database.php';
    $pdo = new PDO(
        "mysql:host={$dbConfig['hostname']};dbname={$dbConfig['database']};charset=utf8mb4",
        $dbConfig['username'],
        $dbConfig['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // 获取POST数据
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        echo json_encode(['status' => 1001, 'msg' => '用户名和密码不能为空']);
        exit;
    }
    
    // 查询用户
    $sql = "SELECT * FROM web_users WHERE u_phone = ? AND u_password = MD5(?)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$username, $password]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode(['status' => 1002, 'msg' => '此账号不存在!']);
        exit;
    }
    
    if ($user['u_status'] == 2) {
        echo json_encode(['status' => 1003, 'msg' => '此账号已被禁用!']);
        exit;
    }
    
    // 启动session
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // 设置session
    $_SESSION['uid'] = $user['u_id'];
    $_SESSION['uphone'] = $user['u_phone'];
    $_SESSION['unickname'] = $user['u_nickname'];
    
    // 更新登录信息
    $updateSql = "UPDATE web_users SET 
                    u_count = u_count + 1,
                    u_this_time = NOW(),
                    u_this_ip = ?,
                    u_last_time = u_this_time,
                    u_last_ip = u_this_ip
                  WHERE u_id = ?";
    $stmt = $pdo->prepare($updateSql);
    $stmt->execute([$_SERVER['REMOTE_ADDR'] ?? '127.0.0.1', $user['u_id']]);
    
    // 记录登录日志
    $logSql = "INSERT INTO web_users_logs (u_id, ul_type, ul_addtime, ul_ip) VALUES (?, 1, NOW(), ?)";
    $stmt = $pdo->prepare($logSql);
    $stmt->execute([$user['u_id'], $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1']);
    
    echo json_encode([
        'status' => 1,
        'msg' => '登录成功',
        'userinfo' => [
            'u_id' => $user['u_id'],
            'u_phone' => $user['u_phone'],
            'u_nickname' => $user['u_nickname']
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 1004,
        'msg' => '登录失败: ' . $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
?>