<?php
/**
 * 快速修复脚本
 * 解决安装后的配置问题
 */

echo "开始修复系统配置...\n";

// 1. 关闭IP白名单检查
$ipConfig = "<?php\nreturn [\n    'onoff' => false, //关闭IP白名单检查\n    'ip' => \"qu.qinghu123.com|f.fcwan.cn|localhost|127.0.0.1\",\n];";
file_put_contents('config/extra/ip.php', $ipConfig);
echo "✓ IP白名单配置已修复\n";

// 2. 检查数据库配置是否需要更新
if(!file_exists('config/database.php')){
    echo "✗ 数据库配置文件不存在，请重新运行安装程序\n";
    exit;
}

$dbConfig = include 'config/database.php';
if($dbConfig['hostname'] === '127.0.0.1' && $dbConfig['database'] === 'qu_qinghu123_com'){
    echo "! 数据库配置使用默认值，建议重新配置\n";
} else {
    echo "✓ 数据库配置正常\n";
}

// 3. 检查必要目录权限
$dirs = ['runtime', 'upload', 'config'];
foreach($dirs as $dir){
    if(!file_exists($dir)){
        mkdir($dir, 0755, true);
    }
    if(!is_writable($dir)){
        echo "! {$dir} 目录权限不足，请手动设置为可写\n";
    } else {
        echo "✓ {$dir} 目录权限正常\n";
    }
}

// 4. 检查安装锁文件
if(file_exists('install.lock')){
    echo "✓ 系统已安装\n";
} else {
    echo "! 未检测到安装锁，系统可能未完全安装\n";
}

echo "\n修复完成！现在可以尝试访问系统：\n";
echo "管理员账号: admin\n";
echo "管理员密码: admin123456\n";
echo "访问地址: http://your-domain/\n";