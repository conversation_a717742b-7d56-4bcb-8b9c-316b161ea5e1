-- ========================================
-- 核心数据库初始化脚本 (简化版)
-- 仅创建必要的表结构和基础数据
-- ========================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 用户表
CREATE TABLE IF NOT EXISTS `web_users` (
  `u_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `u_phone` varchar(20) NOT NULL COMMENT '手机号',
  `u_password` varchar(64) NOT NULL COMMENT '密码',
  `u_nickname` varchar(50) DEFAULT '' COMMENT '昵称',
  `u_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:禁用',
  `u_supermanage` tinyint(1) DEFAULT 1 COMMENT '超管状态 1:普通 2:超级管理员',
  `u_count` int(11) DEFAULT 0 COMMENT '登录次数',
  `u_regtime` datetime DEFAULT NULL COMMENT '注册时间',
  `u_this_time` datetime DEFAULT NULL COMMENT '本次登录时间',
  `u_this_ip` varchar(50) DEFAULT '' COMMENT '本次登录IP',
  `u_last_time` datetime DEFAULT NULL COMMENT '上次登录时间',
  `u_last_ip` varchar(50) DEFAULT '' COMMENT '上次登录IP',
  PRIMARY KEY (`u_id`),
  UNIQUE KEY `uk_phone` (`u_phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户登录日志表
CREATE TABLE IF NOT EXISTS `web_users_logs` (
  `ul_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `u_id` int(11) NOT NULL COMMENT '用户ID',
  `ul_type` tinyint(1) DEFAULT 1 COMMENT '操作类型 1:登录',
  `ul_addtime` datetime NOT NULL COMMENT '操作时间',
  `ul_ip` varchar(50) DEFAULT '' COMMENT '操作IP',
  PRIMARY KEY (`ul_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志表';

-- 权限规则表
CREATE TABLE IF NOT EXISTS `web_auth_rule` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `name` char(80) NOT NULL DEFAULT '' COMMENT '规则唯一标识',
  `title` char(20) NOT NULL DEFAULT '' COMMENT '规则中文名称',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '规则类型',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `condition` char(100) NOT NULL DEFAULT '' COMMENT '规则表达式',
  `said` int(11) DEFAULT 0 COMMENT '上级ID',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限规则表';

-- 权限分组表
CREATE TABLE IF NOT EXISTS `web_auth_group` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `title` char(100) NOT NULL DEFAULT '' COMMENT '分组名称',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `rules` char(80) NOT NULL DEFAULT '' COMMENT '规则ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限分组表';

-- 语音列表表 (补充缺失)
CREATE TABLE IF NOT EXISTS `web_voicelist` (
  `vol_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '语音ID',
  `vol_title` varchar(100) NOT NULL COMMENT '语音标题',
  `vol_content` text COMMENT '语音内容/描述',
  `vol_file_path` varchar(255) DEFAULT '' COMMENT '语音文件路径',
  `vol_status` tinyint(1) DEFAULT 1 COMMENT '状态 1:正常 2:禁用',
  `vol_addtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `vol_updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`vol_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='语音列表表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS `web_system_config` (
  `config_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型',
  `config_group` varchar(30) DEFAULT 'system' COMMENT '配置分组',
  `config_desc` varchar(100) DEFAULT '' COMMENT '配置说明',
  `is_readonly` tinyint(1) DEFAULT 0 COMMENT '是否只读',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`config_id`),
  UNIQUE KEY `uk_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS `web_operation_logs` (
  `ol_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `ol_user_type` tinyint(1) NOT NULL COMMENT '用户类型 1:管理员 2:分站 3:分销',
  `ol_user_id` int(11) NOT NULL COMMENT '用户ID',
  `ol_module` varchar(30) DEFAULT '' COMMENT '模块名',
  `ol_controller` varchar(30) DEFAULT '' COMMENT '控制器名',
  `ol_action` varchar(30) DEFAULT '' COMMENT '操作名',
  `ol_method` varchar(10) DEFAULT '' COMMENT '请求方法',
  `ol_params` text COMMENT '请求参数',
  `ol_ip` varchar(50) DEFAULT '' COMMENT '操作IP',
  `ol_user_agent` varchar(255) DEFAULT '' COMMENT '用户代理',
  `ol_addtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`ol_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

SET FOREIGN_KEY_CHECKS = 1;