<?php
/**
 * MySQL设置助手
 * 帮助用户配置正确的MySQL连接
 */

header("Content-type: text/html; charset=utf-8");

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='utf-8'>
    <title>MySQL设置助手</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔧 MySQL设置助手</h1>
        <p>帮助您解决数据库连接问题并完成系统安装</p>";

echo "<h2>📊 当前问题分析</h2>";
echo "<div class='error'>";
echo "<h4>❌ 主要问题：数据库连接被拒绝</h4>";
echo "<p>所有常见的MySQL连接配置都失败了，这通常是由以下原因造成的：</p>";
echo "<ul>";
echo "<li>MySQL root用户需要密码</li>";
echo "<li>MySQL用户权限配置不正确</li>";
echo "<li>MySQL服务配置问题</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🛠️ 解决方案</h2>";

echo "<h3>方案1: 重置MySQL root密码（推荐）</h3>";
echo "<div class='info'>";
echo "<p>如果您使用的是PHPStudy或类似的集成环境，可以通过以下步骤重置MySQL密码：</p>";
echo "<ol>";
echo "<li>打开PHPStudy控制面板</li>";
echo "<li>点击MySQL服务旁的'管理'按钮</li>";
echo "<li>选择'重置密码'或'修改密码'</li>";
echo "<li>设置root密码为空或记住新密码</li>";
echo "<li>重启MySQL服务</li>";
echo "</ol>";
echo "</div>";

echo "<h3>方案2: 使用命令行重置密码</h3>";
echo "<div class='warning'>";
echo "<p>如果您熟悉命令行操作，可以使用以下步骤：</p>";
echo "<div class='code'>";
echo "# 停止MySQL服务<br>";
echo "net stop mysql<br><br>";
echo "# 以安全模式启动MySQL<br>";
echo "mysqld --skip-grant-tables<br><br>";
echo "# 在新的命令行窗口中连接MySQL<br>";
echo "mysql -u root<br><br>";
echo "# 重置密码<br>";
echo "USE mysql;<br>";
echo "UPDATE user SET authentication_string='' WHERE user='root';<br>";
echo "FLUSH PRIVILEGES;<br>";
echo "EXIT;<br><br>";
echo "# 重启MySQL服务<br>";
echo "net start mysql";
echo "</div>";
echo "</div>";

echo "<h3>方案3: 创建新的数据库用户</h3>";
echo "<div class='info'>";
echo "<p>如果您可以通过其他方式访问MySQL（如phpMyAdmin），可以创建新用户：</p>";
echo "<div class='code'>";
echo "CREATE USER 'installer'@'localhost' IDENTIFIED BY 'install123';<br>";
echo "GRANT ALL PRIVILEGES ON *.* TO 'installer'@'localhost';<br>";
echo "FLUSH PRIVILEGES;";
echo "</div>";
echo "<p>然后在安装时使用用户名：installer，密码：install123</p>";
echo "</div>";

echo "<h2>🧪 测试数据库连接</h2>";

// 如果有POST数据，测试连接
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_connection'])) {
    $hostname = $_POST['hostname'] ?? 'localhost';
    $username = $_POST['username'] ?? 'root';
    $password = $_POST['password'] ?? '';
    $port = $_POST['port'] ?? '3306';
    
    echo "<h3>测试结果</h3>";
    
    try {
        $dsn = "mysql:host=$hostname;port=$port;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5
        ]);
        
        $version = $pdo->query('SELECT VERSION()')->fetchColumn();
        
        echo "<div class='success'>";
        echo "<h4>✅ 连接成功！</h4>";
        echo "<p><strong>MySQL版本:</strong> $version</p>";
        echo "<p>您现在可以使用这些连接信息进行安装。</p>";
        echo "</div>";
        
        // 生成安装链接
        $installUrl = "setup_fixed.html?hostname=" . urlencode($hostname) . 
                     "&username=" . urlencode($username) . 
                     "&password=" . urlencode($password) . 
                     "&port=" . urlencode($port);
        
        echo "<p><a href='$installUrl' class='btn btn-success'>使用这些设置开始安装</a></p>";
        
    } catch (PDOException $e) {
        echo "<div class='error'>";
        echo "<h4>❌ 连接失败</h4>";
        echo "<p><strong>错误信息:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
}

// 连接测试表单
echo "<form method='POST'>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr><td style='padding: 8px; border: 1px solid #ddd;'><strong>主机:</strong></td>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'><input type='text' name='hostname' value='localhost' style='width: 100%; padding: 5px;'></td></tr>";
echo "<tr><td style='padding: 8px; border: 1px solid #ddd;'><strong>端口:</strong></td>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'><input type='text' name='port' value='3306' style='width: 100%; padding: 5px;'></td></tr>";
echo "<tr><td style='padding: 8px; border: 1px solid #ddd;'><strong>用户名:</strong></td>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'><input type='text' name='username' value='root' style='width: 100%; padding: 5px;'></td></tr>";
echo "<tr><td style='padding: 8px; border: 1px solid #ddd;'><strong>密码:</strong></td>";
echo "<td style='padding: 8px; border: 1px solid #ddd;'><input type='password' name='password' value='' style='width: 100%; padding: 5px;'></td></tr>";
echo "</table>";
echo "<p><button type='submit' name='test_connection' class='btn'>测试连接</button></p>";
echo "</form>";

echo "<h2>📋 常见MySQL密码</h2>";
echo "<div class='info'>";
echo "<p>如果您不确定MySQL密码，可以尝试以下常见配置：</p>";
echo "<ul>";
echo "<li><strong>空密码:</strong> 用户名：root，密码：（留空）</li>";
echo "<li><strong>root密码:</strong> 用户名：root，密码：root</li>";
echo "<li><strong>123456:</strong> 用户名：root，密码：123456</li>";
echo "<li><strong>admin:</strong> 用户名：root，密码：admin</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🔗 快速操作</h2>";
echo "<p>";
echo "<a href='setup_fixed.html' class='btn'>返回安装页面</a>";
echo "<a href='install_status.php' class='btn btn-warning'>系统状态检查</a>";
echo "<a href='comprehensive_diagnosis.php' class='btn'>运行完整诊断</a>";
echo "</p>";

echo "<div class='warning'>";
echo "<h4>⚠️ 重要提示</h4>";
echo "<p>一旦数据库连接问题解决，安装过程应该能够正常进行。之前遇到的 \"Unexpected token '<'\" 错误实际上是由于数据库连接失败导致的，而不是JSON解析问题。</p>";
echo "</div>";

echo "    </div>
</body>
</html>";
?>
