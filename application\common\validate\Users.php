<?php
namespace app\common\validate;
use think\Validate;

class Users extends Validate
{
    protected $rule =   [
        'username'  => 'require|length:1,20',  // 支持用户名和手机号，1-20位
        'password'   => 'require|min:6',       // 密码最少6位，不限制最大长度
    ];

    protected $message  =   [
        'username.require' => '用户名必须填写',
		'username.length' => '用户名长度必须在1-20位之间',
        'password.require'   => '密码必须填写',
		'password.min' => '密码长度至少6位',
    ];

    protected $scene = [
        'login'  =>  ['username', 'password'],                    // 登录场景：支持用户名和手机号
        'add'    =>  ['username'=>'require|number|length:11,11', 'password'],  // 添加用户：必须是11位手机号
        'edit'   =>  ['username'=>'require|number|length:11,11', 'password'],  // 编辑用户：必须是11位手机号
    ];

}