# 部署和维护指南

## 🚀 快速部署

### 方式一：Web 安装向导
1. 上传源码到服务器
2. 访问 `http://your-domain/install.php`
3. 按照向导完成环境检查和数据库配置
4. 使用默认管理员账号登录：`admin` / `admin123456`
5. **立即修改默认密码**

### 方式二：手动安装
1. 配置数据库连接 `config/database.php`
2. 执行数据库初始化：
   ```bash
   mysql -u username -p database_name < database_init.sql
   ```
3. 设置目录权限：
   ```bash
   chmod -R 755 runtime/
   chmod -R 755 upload/
   ```

## 📊 系统监控

### 健康检查
```bash
# 命令行检查
php scripts/deploy.php health

# 或通过代码调用
use app\common\service\SystemMonitor;
$status = SystemMonitor::healthCheck();
```

### 监控指标
- **数据库连接状态**
- **文件系统权限**
- **磁盘空间使用**
- **缓存功能状态**
- **核心表完整性**

## 🔒 安全加固

### 生产环境配置
1. **关闭调试模式**：
   ```php
   // config/config.php
   'app_debug' => false,
   'app_trace' => false,
   ```

2. **启用 IP 白名单**：
   ```php
   // config/extra/ip.php
   return [
       'onoff' => true,
       'ip' => 'domain1.com|domain2.com'
   ];
   ```

3. **配置 HTTPS**：
   - 更新 `.htaccess` 强制 HTTPS
   - 更新数据库中的域名配置

### 安全检查清单
- [ ] 删除 `install.php` 文件
- [ ] 修改默认管理员密码
- [ ] 配置防火墙规则
- [ ] 启用 SSL/TLS
- [ ] 定期备份数据库
- [ ] 监控操作日志

## 🔧 日常维护

### 自动化维护脚本
```bash
# 每日备份（建议加入 crontab）
0 2 * * * php /path/to/scripts/deploy.php backup

# 每周清理缓存
0 1 * * 0 php /path/to/scripts/deploy.php cache

# 每月数据库优化
0 3 1 * * php /path/to/scripts/deploy.php optimize
```

### 手动维护操作
```bash
# 清理过期日志（保留30天）
find runtime/log/ -name "*.log" -mtime +30 -delete

# 检查磁盘使用
df -h

# 查看系统负载
top
```

## 📈 性能优化

### 数据库优化
1. **定期执行优化脚本**：
   ```bash
   mysql -u username -p database_name < database_optimization.sql
   ```

2. **监控慢查询**：
   - 启用 MySQL 慢查询日志
   - 定期分析查询性能

### 缓存优化
1. **启用操作缓存**：
   ```php
   // config/config.php
   'cache' => [
       'type'   => 'Redis', // 生产环境建议使用 Redis
       'host'   => '127.0.0.1',
       'port'   => 6379,
   ],
   ```

2. **配置文件缓存**：
   - 启用 OPcache
   - 配置适当的缓存过期时间

## 🔍 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库服务状态
systemctl status mysql

# 检查配置文件
cat config/database.php
```

#### 2. 权限问题
```bash
# 重设目录权限
chmod -R 755 runtime/ upload/
chown -R www-data:www-data .
```

#### 3. 缓存问题
```bash
# 清理所有缓存
php scripts/deploy.php cache
```

### 日志分析
```bash
# 查看系统错误日志
tail -f runtime/log/$(date +%Y%m)/$(date +%d).log

# 查看操作日志
tail -f runtime/deploy/deploy_$(date +%Y-%m-%d).log

# 查看 PHP 错误日志
tail -f /var/log/php_errors.log
```

## 📋 升级指南

### 版本升级流程
1. **备份数据**：
   ```bash
   php scripts/deploy.php backup
   ```

2. **停用系统**（可选）：
   - 设置维护模式页面

3. **更新代码**：
   ```bash
   # 备份当前版本
   cp -r . ../backup_$(date +%Y%m%d)
   
   # 更新源码
   # git pull origin main
   ```

4. **执行升级脚本**：
   ```bash
   php scripts/upgrade.php
   ```

5. **验证功能**：
   - 执行健康检查
   - 测试核心功能

### 数据迁移
如需迁移到新服务器：
1. 导出数据库：`mysqldump > backup.sql`
2. 复制源码文件
3. 导入数据库：`mysql < backup.sql`
4. 更新配置文件
5. 执行健康检查