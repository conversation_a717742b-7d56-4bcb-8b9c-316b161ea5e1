<?php
/**
 * 最小化安装测试
 * 用于隔离JSON响应问题
 */

// 立即关闭所有输出
error_reporting(0);
ini_set('display_errors', 0);

// 处理POST请求
if($_SERVER['REQUEST_METHOD'] === 'POST'){
    // 立即开始输出缓冲
    ob_start();
    
    try {
        // 清理任何意外输出
        ob_clean();
        
        // 设置JSON响应头
        header('Content-Type: application/json; charset=utf-8');
        
        // 获取POST数据
        $step = $_POST['step'] ?? '';
        $hostname = $_POST['hostname'] ?? '';
        $database = $_POST['database'] ?? '';
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        $hostport = $_POST['hostport'] ?? '3306';
        
        // 模拟处理
        if($step === 'install'){
            // 简单的响应测试
            if(empty($database)){
                echo json_encode([
                    'status' => false,
                    'message' => '数据库名不能为空'
                ], JSON_UNESCAPED_UNICODE);
                exit;
            }
            
            // 测试数据库连接
            $link = @mysqli_connect($hostname, $username, $password, '', intval($hostport));
            if(!$link){
                echo json_encode([
                    'status' => false,
                    'message' => '数据库连接失败: ' . mysqli_connect_error()
                ], JSON_UNESCAPED_UNICODE);
                exit;
            }
            
            // 创建数据库
            $sql = "CREATE DATABASE IF NOT EXISTS `{$database}` DEFAULT CHARSET utf8mb4";
            if(!mysqli_query($link, $sql)){
                echo json_encode([
                    'status' => false,
                    'message' => '创建数据库失败: ' . mysqli_error($link)
                ], JSON_UNESCAPED_UNICODE);
                mysqli_close($link);
                exit;
            }
            
            mysqli_close($link);
            
            // 成功响应
            echo json_encode([
                'status' => true,
                'message' => '测试成功！数据库连接正常，数据库已创建。'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        // 无效步骤
        echo json_encode([
            'status' => false,
            'message' => '无效的安装步骤'
        ], JSON_UNESCAPED_UNICODE);
        exit;
        
    } catch (Exception $e) {
        ob_clean();
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'status' => false,
            'message' => '安装异常: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// GET请求显示简单信息
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>最小化安装测试</title>
</head>
<body>
    <h1>最小化安装测试</h1>
    <p>这是一个简化的安装接口，用于测试JSON响应问题。</p>
    <p>请使用 setup.html 或 debug_install_response.php 来测试此接口。</p>
    
    <form method="post">
        <h3>快速测试</h3>
        <input type="hidden" name="step" value="install">
        <table>
            <tr><td>主机:</td><td><input type="text" name="hostname" value="localhost"></td></tr>
            <tr><td>端口:</td><td><input type="text" name="hostport" value="3306"></td></tr>
            <tr><td>数据库:</td><td><input type="text" name="database" value="test_db"></td></tr>
            <tr><td>用户名:</td><td><input type="text" name="username" value="root"></td></tr>
            <tr><td>密码:</td><td><input type="password" name="password"></td></tr>
        </table>
        <button type="submit">测试连接</button>
    </form>
</body>
</html>